# 计算器关联图分析器 (CmdCalculatorGraphAnalyzer)

## 功能描述

这个Command用于分析GDMP文档中所有图元的关联更新机制，并生成Mermaid格式的关联图。

## 主要功能

1. **遍历所有图元**: 通过`IDocument::GetAllElements()`获取文档中的所有图元
2. **获取关联更新组件**: 对每个图元调用`GetElementRegenerationComponent()`
3. **收集计算器**: 调用`GetCalculators()`获取图元的所有计算器
4. **分析输入输出关系**: 
   - 通过`GetRegenDataId()`获取计算器的输出数据ID
   - 通过`ReportInputDataIds()`获取计算器的输入数据ID
5. **生成Mermaid图**: 将计算器之间的依赖关系转换为Mermaid图形语法

## 核心数据结构

```cpp
struct CalculatorInfo
{
    std::wstring calculatorName;    // 计算器名称
    std::wstring elementId;         // 所属图元ID
    std::wstring elementType;       // 图元类型
    RegenDataId outputDataId;       // 输出数据ID
    std::vector<RegenDataId> inputDataIds; // 输入数据ID列表
};
```

## 关键算法

### 1. 图元遍历
```cpp
std::vector<IElement*> allElements = pDoc->GetAllElements();
for (IElement* pElement : allElements)
{
    IElementRegenerationComponent* pRegenComponent = pElement->GetElementRegenerationComponent();
    if (pRegenComponent)
    {
        // 分析计算器...
    }
}
```

### 2. 计算器收集
```cpp
OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pDoc);
pRegenComponent->GetCalculators(opCalculators.get());

int calculatorCount = opCalculators->GetCalculatorCount();
for (int i = 0; i < calculatorCount; i++)
{
    const ICalculator* pCalculator = opCalculators->GetCalcualtorByIndex(i);
    // 分析单个计算器...
}
```

### 3. 依赖关系分析
```cpp
// 获取输出
RegenDataId outputDataId = pCalculator->GetRegenDataId();

// 获取输入
std::vector<RegenDataId> inputDataIds;
pCalculator->ReportInputDataIds(inputDataIds);
```

### 4. Mermaid图生成
```cpp
// 创建节点
std::wstring nodeId = L"calc" + std::to_wstring(i);
std::wstring nodeLabel = calculatorName + L"<br/>(" + elementType + L":" + elementId + L")";

// 创建连接
for (const RegenDataId& inputDataId : inputDataIds)
{
    // 查找输入数据对应的输出计算器
    // 生成 "sourceNode --> targetNode" 连接
}
```

## 输出格式

生成的Mermaid图保存为`calculator_graph.md`文件，格式如下：

```markdown
# 计算器关联更新图

```mermaid
graph TD
    calc0["CalculatorName<br/>(ElementType:ElementId)"]
    calc1["AnotherCalculator<br/>(ElementType:ElementId)"]
    calc0 --> calc1
```
```

## 使用方法

1. 在GDMP应用中注册并执行`CmdCalculatorGraphAnalyzer`命令
2. 命令会自动分析当前文档中的所有图元
3. 生成的Mermaid图保存在当前目录的`calculator_graph.md`文件中
4. 可以使用支持Mermaid的工具（如Typora、GitHub、VS Code等）查看图形

## 扩展功能

可以进一步扩展以下功能：

1. **过滤特定类型的图元**: 只分析特定类型的图元
2. **图形美化**: 为不同类型的计算器使用不同的颜色和形状
3. **交互式输出**: 生成HTML格式的交互式图形
4. **性能优化**: 对大型文档进行分批处理
5. **详细信息**: 在节点中显示更多计算器和图元信息

## 技术要点

- 使用`RegenDataId`的`ObjectId`来关联图元
- 通过比较`RegenDataId`的完整信息来建立输入输出关系
- 使用STL容器（map、set）来避免重复连接
- 支持Unicode字符串处理（std::wstring）
