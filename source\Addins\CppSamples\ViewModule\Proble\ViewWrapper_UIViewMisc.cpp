﻿#include "ViewWrapper.h"
#include "GbmpNew.h"
#include "IUiView.h"
#include "ICanvas.h"
#include "ICamera.h"
#include "IUiView.h"
#include "IDocument.h"
#include "IModelView.h"
#include "ISkyOptions.h"
#include "IUiDocument.h"
#include "IZebraManager.h"
#include "ICanvasOptions.h"
#include "IShadowOptions.h"
#include "IViewShipOptions.h"
#include "IHaloedGapManager.h"
#include "IHighlightSetting.h"
#include "IViewHouseOptions.h"
#include "UiDocumentViewUtils.h"
#include "IPreHighlightSetting.h"
#include "IVisualEffectManager.h"
#include "IRenderEngineContext.h"
#include "IUiDocumentViewManager.h"
#include "ICurvatureGraphManager.h"
#include "IHatchPatternMaxLinesManager.h"

#include "INavigateManager.h"

#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

// 视图显示探针中UiView等对象属性函数的包装，以及ViewWrapper中的公共辅助函数

using namespace gcmp;
using namespace Sample;

gcmp::OwnerPtr<ViewWrapper> Sample::ViewWrapper::VisitUiView()
{
    IUiView* pUiView = GetUiView();
    if (nullptr == pUiView)
    {
        return nullptr;
    }

    ParametersArray parameters;

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetId", pUiView->GetId());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetTag", pUiView->GetTag());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSubUiView", pUiView->IsSubUiView());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDefaultActionCommand", pUiView->GetDefaultActionCommand());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetCursorPath", pUiView->GetCursorPath());
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txtValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            pUiView->SetCursorPath(txtValue);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetDevicePixelRatio", pUiView->GetDevicePixelRatio());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWindowWidth", pUiView->GetWindowWidth());
        IFieldProxy::SetIntFieldFunc setIntFunc = [&](int intValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            pUiView->SetWindowWidth(intValue);
        };
        opField->SetSetIntFieldFunc(setIntFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWindowHeight", pUiView->GetWindowHeight());
        IFieldProxy::SetIntFieldFunc setIntFunc = [&](int intValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            pUiView->SetWindowHeight(intValue);
        };
        opField->SetSetIntFieldFunc(setIntFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        Vector2i windowPosition = pUiView->GetWindowPosition();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWindowPositionX", windowPosition.X());
        IFieldProxy::SetIntFieldFunc setIntFunc = [&](int intValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            Vector2i windowPosition = pUiView->GetWindowPosition();
            pUiView->SetWindowPosition(intValue, windowPosition.Y());
        };
        opField->SetSetIntFieldFunc(setIntFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        Vector2i windowPosition = pUiView->GetWindowPosition();
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetWindowPositionY", windowPosition.Y());
        IFieldProxy::SetIntFieldFunc setIntFunc = [&](int intValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            Vector2i windowPosition = pUiView->GetWindowPosition();
            pUiView->SetWindowPosition(windowPosition.X(), intValue);
        };
        opField->SetSetIntFieldFunc(setIntFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"SetTitle", pUiView->GetTitle());
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring txtValue)->void
        {
            IUiView* pUiView = GetUiView();
            if (nullptr == pUiView)
            {
                return;
            }
            pUiView->SetTitle(txtValue);
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsMaximized", pUiView->IsMaximized());
        parameters.push_back(TransferOwnership(opField));
    }

    return TransferOwnership(ViewWrapper::Create(L"IUiView", TransferOwnership(parameters)));
}

void Sample::ViewWrapper::RunSetFieldFuncs()
{
    for (auto& pair : GetParameters())
    {
        for (auto& itor : pair.second)
        {
            itor->RunSetFieldFunc();
        }
    }
}

gcmp::IUiView* Sample::ViewWrapper::GetUiView()
{
    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    return pUiDocViewMgr->GetCurrentUiView();
}

gcmp::ICanvas * Sample::ViewWrapper::GetCanvas()
{
    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    IUiView* pCurrentView = pUiDocViewMgr->GetCurrentUiView();
    if (pCurrentView == nullptr)
    {
        return nullptr;
    }
    return pCurrentView->GetCanvas();
}

gcmp::ICamera * Sample::ViewWrapper::GetCamera()
{
    ICanvas* pCanvas = GetCanvas();
    if (pCanvas == nullptr)
    {
        return nullptr;
    }
    return pCanvas->GetCamera();
}

gcmp::IModelView * Sample::ViewWrapper::GetModelView()
{
    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    IUiView* pCurrentView = pUiDocViewMgr->GetCurrentUiView();
    if (pCurrentView == nullptr)
    {
        return nullptr;
    }
    return pCurrentView->GetModelView();
}
gcmp::ICanvasOptions* Sample::ViewWrapper::GetCanvasOptions()
{
    ICanvas* pCanvas = GetCanvas();
    if (nullptr == pCanvas)
    {
        return nullptr;
    }
    return pCanvas->GetOptions();
}
gcmp::IHaloedGapManager* Sample::ViewWrapper::GetIHaloedGapManager()
{
    ICanvas* pCanvas = GetCanvas();
    if (nullptr == pCanvas)
    {
        return nullptr;
    }
    return pCanvas->GetIHaloedGapManager();
}
gcmp::IPreHighlightSetting* Sample::ViewWrapper::GetIPreHighlightSetting(gcmp::PreHighlightType preHighlightType)
{
    IVisualEffectManager* pIVisualEffectManager = IVisualEffectManager::Get();
    if (nullptr == pIVisualEffectManager)
    {
        return nullptr;
    }
    return pIVisualEffectManager->GetPreHighlightSetting(preHighlightType);
}
gcmp::IHighlightSetting* Sample::ViewWrapper::GetIHighlightSetting()
{
    IVisualEffectManager* pIVisualEffectManager = IVisualEffectManager::Get();
    if (nullptr == pIVisualEffectManager)
    {
        return nullptr;
    }
    return pIVisualEffectManager->GetHighlightSetting();
}
gcmp::IShadowOptions* Sample::ViewWrapper::GetIShadowOptions()
{
    ICanvasOptions* pICanvasOptions = GetCanvasOptions();
    if (nullptr == pICanvasOptions)
    {
        return nullptr;
    }
    return pICanvasOptions->GetShadowOptions();
}
gcmp::ISkyOptions* Sample::ViewWrapper::GetISkyOptions()
{
    ICanvasOptions* pICanvasOptions = GetCanvasOptions();
    if (nullptr == pICanvasOptions)
    {
        return nullptr;
    }
    return pICanvasOptions->GetSkyOptions();
}
gcmp::IViewShipOptions* Sample::ViewWrapper::GetIViewShipOptions()
{
    ICanvasOptions* pICanvasOptions = GetCanvasOptions();
    if (nullptr == pICanvasOptions)
    {
        return nullptr;
    }
    return pICanvasOptions->GetViewShipOptions();
}
gcmp::IViewHouseOptions* Sample::ViewWrapper::GetIViewHouseOptions()
{
    ICanvasOptions* pICanvasOptions = GetCanvasOptions();
    if (nullptr == pICanvasOptions)
    {
        return nullptr;
    }
    return pICanvasOptions->GetViewHouseOptions();
}

gcmp::IHatchPatternMaxLinesManager* Sample::ViewWrapper::GetHatchPatternMaxLinesManager()
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    IHatchPatternMaxLinesManager* pHatchMaxLineMgr = IHatchPatternMaxLinesManager::Get(pDoc);
    return pHatchMaxLineMgr;
}

gcmp::IZebraManager* GetZebraManager()
{
    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    IUiDocument* pUiDoc = pUiDocViewMgr->GetCurrentUiDocument();
    if (!pUiDoc)return nullptr;
    IDocument* pDoc = pUiDoc->GetDbDocument();
    if (!pDoc)return nullptr;
    return pDoc->GetZebraManager();
}

gcmp::ICurvatureGraphManager* GetCurvatureGraphManager()
{
    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    IUiDocument* pUiDoc = pUiDocViewMgr->GetCurrentUiDocument();
    if (!pUiDoc)return nullptr;
    IDocument* pDoc = pUiDoc->GetDbDocument();
    if (!pDoc)return nullptr;
    return pDoc->GetCurvatureGraphManager();
}

const gcmp::ZebraOptions GetZebraOptions()
{
    IZebraManager* zebraMgr = GetZebraManager();
    if (!zebraMgr)return ZebraOptions();
    return zebraMgr->GetZebraOptions();
}

const gcmp::CurvatureGraphOptions GetCurvatureGraphOptions()
{
    ICurvatureGraphManager* pCurvatureGraphMgr = GetCurvatureGraphManager();
    if (!pCurvatureGraphMgr)return CurvatureGraphOptions();
    CurvatureGraphOptions options;
    //pCurvatureGraphMgr->GetCurvatureGraphOption()
    return options;
}

gcmp::OwnerPtr<ViewWrapper> ViewWrapper::VisitRenderEngineContext()
{
    gcmp::IRenderEngineContext* pContext = gcmp::IRenderEngineContext::GetInstance();
    ParametersArray parameters;

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsOnSoftwareMode", pContext->IsOnSoftwareMode());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"RenderEngineErrorCode", pContext->GetRenderEngineErrorCode());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        gcmp::RenderSystem renderSystem = pContext->GetRenderSystem();
        std::wstring renderSystemStr;
        switch (renderSystem)
        {
        case gcmp::RenderSystem::Unknown:
            renderSystemStr = L"Unknown";
            break;
        case gcmp::RenderSystem::OpenGL:
            renderSystemStr = L"OpenGL";
            break;
        case gcmp::RenderSystem::DirectX9:
            renderSystemStr = L"DirectX9";
            break;
        case gcmp::RenderSystem::DirectX11:
            renderSystemStr = L"DirectX11";
            break;
        case gcmp::RenderSystem::Angle:
            renderSystemStr = L"Angle";
            break;
        case gcmp::RenderSystem::DirectX12:
            renderSystemStr = L"DirectX12";
            break;
        default:
            renderSystemStr = L"Unknown";
            break;
        }

        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"RenderSystem", renderSystemStr);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"WindowsOSVersion", pContext->WindowsOSVersion());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUCount", pContext->GPUCount());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUUsed", pContext->GPUUsed());
        parameters.push_back(TransferOwnership(opField));
    }

    for (int i = 0; i < pContext->GPUCount(); i++)
    {
        std::wstring iStr = StringUtil::ToWString(i);

        OwnerPtr<IFieldProxy> opField0 = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUD3DFeatureLevel_" + iStr, pContext->GPUD3DFeatureLevel(i));
        parameters.push_back(TransferOwnership(opField0));

        std::wstring GPUVendor = L"NVIDIA";
        switch (pContext->GPUVendor(i))
        {
        case GraphicsCardVendor::NVIDIA:
            GPUVendor = L"NVIDIA";
            break;
        case GraphicsCardVendor::AMD:
            GPUVendor = L"AMD";
            break;
        case GraphicsCardVendor::Intel:
            GPUVendor = L"Intel";
            break;
        case GraphicsCardVendor::Unknown:
        default:
            GPUVendor = L"Unknown";
            break;
        }

        OwnerPtr<IFieldProxy> opField1 = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUVendor_" + iStr, GPUVendor);
        parameters.push_back(TransferOwnership(opField1));

        OwnerPtr<IFieldProxy> opField2 = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUDriverDate_" + iStr, pContext->GPUDriverDate(i));
        parameters.push_back(TransferOwnership(opField2));

        OwnerPtr<IFieldProxy> opField3 = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUCaption_" + iStr, pContext->GPUCaption(i));
        parameters.push_back(TransferOwnership(opField3));

        OwnerPtr<IFieldProxy> opField4 = NEW_AS_OWNER_PTR(IFieldProxy, L"GPUID_" + iStr, pContext->GPUID(i));
        parameters.push_back(TransferOwnership(opField4));
    }

    {
        gcmp::HardWareInfo hardwareInfo;
        pContext->GetHardWareInfo(hardwareInfo);

        OwnerPtr<IFieldProxy> opField1 = NEW_AS_OWNER_PTR(IFieldProxy, L"ProcessorName", hardwareInfo.ProcessorName);
        parameters.push_back(TransferOwnership(opField1));

        OwnerPtr<IFieldProxy> opField2 = NEW_AS_OWNER_PTR(IFieldProxy, L"DefDispalyCardName", hardwareInfo.DefDispalyCardName);
        parameters.push_back(TransferOwnership(opField2));

        OwnerPtr<IFieldProxy> opField3 = NEW_AS_OWNER_PTR(IFieldProxy, L"OSName", hardwareInfo.OSName);
        parameters.push_back(TransferOwnership(opField3));

        OwnerPtr<IFieldProxy> opField4 = NEW_AS_OWNER_PTR(IFieldProxy, L"OSArchitecture", hardwareInfo.OSArchitecture);
        parameters.push_back(TransferOwnership(opField4));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSupportDrawInstanceData", pContext->IsSupportDrawInstanceData());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSupportRSCFBO", pContext->IsSupportRSCFBO());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        int nMajor, nMinor;
        float fVer;
        pContext->GetOpenGLVersion(nMajor, nMinor, fVer);

        OwnerPtr<IFieldProxy> opField1 = NEW_AS_OWNER_PTR(IFieldProxy, L"OpenGLMajorVersion", nMajor);
        parameters.push_back(TransferOwnership(opField1));

        OwnerPtr<IFieldProxy> opField2 = NEW_AS_OWNER_PTR(IFieldProxy, L"OpenGLMinorVersion", nMinor);
        parameters.push_back(TransferOwnership(opField2));

        OwnerPtr<IFieldProxy> opField3 = NEW_AS_OWNER_PTR(IFieldProxy, L"OpenGLVersion", fVer);
        parameters.push_back(TransferOwnership(opField3));
    }

    {
        gcmp::MonitorsPrimaryAdapterProblem monitorProblem = pContext->CheckMonitors();
        std::wstring monitorProblemStr;
        switch (monitorProblem)
        {
        case gcmp::MonitorsPrimaryAdapterProblem::Error_DisplayCardError:
            monitorProblemStr = L"Error_DisplayCardError";
            break;
        case gcmp::MonitorsPrimaryAdapterProblem::Switch_DisplayAdapter:
            monitorProblemStr = L"Switch_DisplayAdapter";
            break;
        case gcmp::MonitorsPrimaryAdapterProblem::Error_MonitorMissing:
            monitorProblemStr = L"Error_MonitorMissing";
            break;
        case gcmp::MonitorsPrimaryAdapterProblem::MonitorAdaptorOK:
            monitorProblemStr = L"MonitorAdaptorOK";
            break;
        case gcmp::MonitorsPrimaryAdapterProblem::Unknown:
            monitorProblemStr = L"Unknown";
            break;
        default:
            monitorProblemStr = L"Unknown";
            break;
        }

        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"MonitorsPrimaryAdapterProblem", monitorProblemStr);
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"DriverVersion", pContext->GetDriverVersion());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsSupportRSCVBO", pContext->IsSupportRSCVBO());
        parameters.push_back(TransferOwnership(opField));
    }

    {
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"IsLaptop", pContext->IsLaptop());
        parameters.push_back(TransferOwnership(opField));
    }

    return TransferOwnership(ViewWrapper::Create(L"IRenderEngineContext", TransferOwnership(parameters)));
}

gcmp::OwnerPtr<ViewWrapper> ViewWrapper::VisitNavigateManager()
{
    INavigateManager& navMgr = INavigateManager::GetInstance();
    INavigateManager::RotateStrategy rotateStrategy = navMgr.GetRotateStrategy();

    ParametersArray parameters;
    {
        std::wstring rotateStrategyStr = L"";
        if (rotateStrategy == INavigateManager::RotateStrategy::CtrlAndMiddleButton)
        {
            rotateStrategyStr = L"CtrlAndMiddleButton";
        }
        else if (rotateStrategy == INavigateManager::RotateStrategy::RightButton)
        {
            rotateStrategyStr = L"RightButton";
        }
        OwnerPtr<IFieldProxy> opField = NEW_AS_OWNER_PTR(IFieldProxy, L"GetRotateStrategy", rotateStrategyStr);
        IFieldProxy::SetTextOrEnumFieldFunc setTxtFunc = [&](std::wstring strValue)->void
        {
            INavigateManager& navMgr = INavigateManager::GetInstance();
            if (strValue == L"CtrlAndMiddleButton")
            {
                navMgr.SetRotateStrategy(INavigateManager::RotateStrategy::CtrlAndMiddleButton);
            }
            else if (strValue == L"RightButton")
            {
                navMgr.SetRotateStrategy(INavigateManager::RotateStrategy::RightButton);
            }
        };
        opField->SetSetEnumFieldFunc(setTxtFunc);
        parameters.push_back(TransferOwnership(opField));
    }
    return TransferOwnership(ViewWrapper::Create(L"INavigateManager", TransferOwnership(parameters)));
}