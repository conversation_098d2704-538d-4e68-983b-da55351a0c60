# 计算器关联更新图示例

这是一个由CmdCalculatorGraphAnalyzer生成的示例输出文件。

分析了 6 个计算器

```mermaid
graph TD
    calc0["墙参数计算器<br/>(Wall:12345)"]
    calc1["墙图形计算器<br/>(Wall:12345)"]
    calc2["楼板厚度计算器<br/>(Floor:67890)"]
    calc3["楼板图形计算器<br/>(Floor:67890)"]
    calc4["梁截面计算器<br/>(Beam:11111)"]
    calc5["梁图形计算器<br/>(Beam:11111)"]
    
    calc0 --> calc1
    calc2 --> calc3
    calc4 --> calc5
    calc1 --> calc3
    calc5 --> calc3
    
    style calc0 fill:#e1f5fe
    style calc1 fill:#e1f5fe
    style calc2 fill:#f3e5f5
    style calc3 fill:#f3e5f5
    style calc4 fill:#e8f5e8
    style calc5 fill:#e8f5e8
```

## 分析结果说明

### 图元类型
- **墙 (Wall)**: 蓝色节点，包含参数计算器和图形计算器
- **楼板 (Floor)**: 紫色节点，包含厚度计算器和图形计算器  
- **梁 (Beam)**: 绿色节点，包含截面计算器和图形计算器

### 依赖关系
1. 墙参数计算器 → 墙图形计算器
2. 楼板厚度计算器 → 楼板图形计算器
3. 梁截面计算器 → 梁图形计算器
4. 墙图形计算器 → 楼板图形计算器 (墙与楼板的关联)
5. 梁图形计算器 → 楼板图形计算器 (梁与楼板的关联)

### 关联更新机制
当墙的参数发生变化时，会触发以下更新链：
1. 墙参数计算器重新计算
2. 墙图形计算器重新计算
3. 楼板图形计算器重新计算（因为楼板依赖墙的图形）

类似地，当梁的截面发生变化时：
1. 梁截面计算器重新计算
2. 梁图形计算器重新计算
3. 楼板图形计算器重新计算（因为楼板依赖梁的图形）

这种机制确保了建筑模型中各构件之间的几何关系始终保持一致。
