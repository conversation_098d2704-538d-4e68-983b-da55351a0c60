#pragma once

#include "CommandBase.h"
#include "RegenDataId.h"
#include <vector>
#include <string>
#include <map>
#include <set>
#include "ElementId.h"

namespace gcmp
{
    class IDocument;
    class IElement;
}

using namespace gcmp;

namespace Sample
{
    class CmdCalculatorGraphAnalyzer : public CommandBase
    {
    public:
        CmdCalculatorGraphAnalyzer();

    public:
        virtual OwnerPtr<IAction> ExecuteCommand(const gcmp::CommandParameters& cmdParams) override;
        virtual bool IsEnabled() const override { return true; }
        virtual bool IsVisible() const override { return true; }
        virtual bool ShouldClearSelectionBeforeExecution() const override { return false; }

    private:
        struct CalculatorInfo
        {
            std::wstring calculatorName;
            std::wstring elementId;
            std::wstring elementType;
            RegenDataId outputDataId;
            std::vector<RegenDataId> inputDataIds;
            const IElement* pElement;  // 图元指针，用于分组
        };

        void AnalyzeElementCalculators(IDocument* pDoc, std::vector<CalculatorInfo>& calculatorInfos);
        std::wstring GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos);
        std::wstring RegenDataIdToString(const RegenDataId& dataId);
        std::wstring RegenDataIdToKey(const RegenDataId& dataId);
        std::wstring GetRegenDataIdReadableInfo(const RegenDataId& dataId);
        void DFSFindElementGroup(const ElementId& elementId, const std::map<ElementId, std::vector<ElementId>>& connections,
                                std::set<ElementId>& visited, std::vector<ElementId>& group);
        void FindReachableElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                  std::set<ElementId>& reachableElements);
        void FindConnectedElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                  std::set<ElementId>& connectedElements);
        void FindCalculatorsOnPathToTarget(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                          std::set<size_t>& calculatorsOnPath);
        bool HasDirectPathBetweenCalculators(const std::vector<CalculatorInfo>& calculatorInfos,
                                            size_t fromCalc, size_t toCalc, std::set<size_t>& visited);
        void ApplyNodeStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                           const std::set<size_t>& relevantCalculators,
                           const std::map<std::wstring, std::wstring>& regenDataIdToNodeId,
                           const std::vector<std::wstring>& calculatorColors, size_t totalElements);
        void ApplyRegenDataIdStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                                  const std::set<size_t>& relevantCalculators,
                                  const std::map<std::wstring, std::wstring>& regenDataIdToNodeId,
                                  const std::map<size_t, size_t>& calculatorToColorIndex,
                                  const std::map<size_t, std::set<size_t>>& upstreamToTargetConnections,
                                  const std::map<size_t, std::set<size_t>>& downstreamToTargetConnections);
        void ApplyEdgeStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                           const std::set<size_t>& relevantCalculators,
                           const std::map<std::wstring, std::wstring>& regenDataIdToNodeId);
        std::wstring GetElementTypeName(const IElement* pElement);

        // 新增方法：按计算器分别导出和单文件导出
        bool ExportMermaidByCalculators(const std::vector<CalculatorInfo>& calculatorInfos);
        bool ExportSingleMermaidFile(const std::wstring& mermaidGraph);
        std::wstring GenerateMermaidGraphForCalculator(const std::vector<CalculatorInfo>& calculatorInfos, size_t targetCalculatorIndex);
        bool SaveMermaidToFile(const std::wstring& content, const std::wstring& filePath);

        // 控制是否只显示多图元组
        bool m_onlyMultipleElements = true;

        // 指定要分析的ElementId，如果IsValid()为true，则只输出与此ElementId相关的组
        ElementId m_targetElementId;
    };
}
