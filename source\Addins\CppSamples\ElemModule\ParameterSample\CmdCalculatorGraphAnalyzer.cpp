#include "CmdCalculatorGraphAnalyzer.h"
#include "ElemModule.h"
#include "ElemModuleCommandIds.h"
#include "CommandRegister.h"
#include "IDocument.h"
#include "IModelView.h"
#include "UiDocumentViewUtils.h"
#include "IUserTransaction.h"
#include "IApplicationWindow.h"
#include "IApplication.h"
#include "ICommandManager.h"
#include "IElement.h"
#include "IElementRegenerationComponent.h"
#include "ICalculatorCollection.h"
#include "ICalculator.h"
#include "RegenDataId.h"
#include "ElementId.h"
#include "UiCommonDialog.h"
#include "FileUtility.h"
#include "../RegenDataIdManager.h"
#include <sstream>
#include <fstream>
#include <set>
#include <map>
#include <queue>
#include <algorithm>
#include <Windows.h>
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

#define ID_CMD_CALCULATOR_GRAPH_ANALYZER L"CmdCalculatorGraphAnalyzer"
#define ID_CMD_CALCULATOR_GRAPH_ANALYZER_BY_SELECTION L"CmdCalculatorGraphAnalyzerBySelection"

CmdCalculatorGraphAnalyzer::CmdCalculatorGraphAnalyzer()
    :CommandBase(ID_CMD_CALCULATOR_GRAPH_ANALYZER, true)
{
}

gcmp::OwnerPtr<gcmp::IAction> CmdCalculatorGraphAnalyzer::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (!pDoc)
    {
        // 可以添加用户提示：没有打开的文档
        return nullptr;
    }

    std::vector<CalculatorInfo> calculatorInfos;
    AnalyzeElementCalculators(pDoc, calculatorInfos);

    if (calculatorInfos.empty())
    {
        // 可以添加用户提示：没有找到计算器
        return nullptr;
    }

    // 检查CommandParameters中是否有ElementId参数
    auto elementIdParam = cmdParams.find(L"ElementId");
    if (elementIdParam != cmdParams.end())
    {
        // 从参数中获取ElementId
        Int64 idNum = elementIdParam->second.AsInt64();
        m_targetElementId = ElementId(idNum);
    }
    else
    {
        // 弹出对话框让用户输入ElementId
        std::wstring idStr;
        Int64 idNum = -1;
        if (UiCommonDialog::ShowInputBox(L"搜索视图", L"输入图元ElementId", L"", idStr) != UiCommonDialog::ButtonType::OK)
        {
            // 用户取消了输入，返回nullptr
            return nullptr;
        }

        idNum = StringUtil::ToNumber<Int64>(idStr);
        m_targetElementId = ElementId(idNum);
    }

    // 询问用户是否按计算器分别导出
    gcmp::UiCommonDialog::ButtonType exportChoice = gcmp::UiCommonDialog::ShowMessageBox(
        L"导出选项",
        L"是否按目标图元的每个计算器分别导出为多个文件？\n\n是：每个计算器导出一个文件\n否：导出为单个完整文件",
        (int)gcmp::UiCommonDialog::ButtonType::Yes | (int)gcmp::UiCommonDialog::ButtonType::No);

    if (exportChoice == gcmp::UiCommonDialog::ButtonType::Yes)
    {
        // 按计算器分别导出
        bool success = ExportMermaidByCalculators(calculatorInfos);
        if (!success)
        {
            // 用户取消了文件保存，返回nullptr
            return nullptr;
        }
    }
    else if (exportChoice == gcmp::UiCommonDialog::ButtonType::No)
    {
        // 导出单个完整文件
        std::wstring mermaidGraph = GenerateMermaidGraph(calculatorInfos);
        bool success = ExportSingleMermaidFile(mermaidGraph);
        if (!success)
        {
            // 用户取消了文件保存，返回nullptr
            return nullptr;
        }
    }
    // 用户在导出选择对话框中选择了取消，或者其他情况
    return nullptr;
}

bool CmdCalculatorGraphAnalyzer::ExportSingleMermaidFile(const std::wstring& mermaidGraph)
{
    // 将Mermaid图保存到文件
    std::wstring filePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(GBMP_TR(L"请选择导出mermaid文件"),
        L"", GBMP_TR(L"markdown文件(*.mmd)"));

    if (filePathName.empty())
    {
        // 用户取消了文件保存，返回false
        return false;
    }

    return SaveMermaidToFile(mermaidGraph, filePathName);
}

bool CmdCalculatorGraphAnalyzer::ExportMermaidByCalculators(const std::vector<CalculatorInfo>& calculatorInfos)
{
    // 获取基础文件路径
    std::wstring baseFilePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(GBMP_TR(L"请选择导出mermaid文件的基础路径"),
        L"", GBMP_TR(L"markdown文件(*.mmd)"));

    if (baseFilePathName.empty())
    {
        // 用户取消了文件保存，返回false
        return false;
    }

    // 移除扩展名，准备添加计算器名称后缀
    std::wstring basePath = baseFilePathName;
    size_t dotPos = basePath.find_last_of(L'.');
    if (dotPos != std::wstring::npos)
    {
        basePath = basePath.substr(0, dotPos);
    }

    // 找到目标图元的所有计算器
    std::vector<size_t> targetCalculators;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
        {
            targetCalculators.push_back(i);
        }
    }

    // 为每个目标计算器生成单独的mermaid文件
    for (size_t calcIndex : targetCalculators)
    {
        std::wstring calculatorGraph = GenerateMermaidGraphForCalculator(calculatorInfos, calcIndex);

        // 生成文件名：基础路径 + 计算器名称 + .mmd
        std::wstring calculatorName = calculatorInfos[calcIndex].calculatorName;
        // 替换文件名中的非法字符
        std::replace(calculatorName.begin(), calculatorName.end(), L':', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'/', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'\\', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'*', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'?', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'"', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'<', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'>', L'_');
        std::replace(calculatorName.begin(), calculatorName.end(), L'|', L'_');

        std::wstring outputFilePath = basePath + L"_" + calculatorName + L".mmd";

        SaveMermaidToFile(calculatorGraph, outputFilePath);
    }

    return true; // 所有文件都已成功处理
}

bool CmdCalculatorGraphAnalyzer::SaveMermaidToFile(const std::wstring& content, const std::wstring& filePath)
{
    // 将wstring转换为UTF-8编码的string
    int utf8Size = WideCharToMultiByte(CP_UTF8, 0, content.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (utf8Size > 0)
    {
        std::vector<char> utf8Buffer(utf8Size);
        WideCharToMultiByte(CP_UTF8, 0, content.c_str(), -1, utf8Buffer.data(), utf8Size, nullptr, nullptr);

        // 去掉末尾的null终止符
        bool success = gcmp::FileUtility::Save(filePath, utf8Buffer.data(), utf8Size - 1);
        if (success)
        {
            // 可以添加用户提示：文件保存成功
            return true;
        }
        else
        {
            // 可以添加用户提示：文件保存失败
            return false;
        }
    }
    else
    {
        // 可以添加用户提示：字符串转换失败
        return false;
    }
}

std::wstring CmdCalculatorGraphAnalyzer::GenerateMermaidGraphForCalculator(const std::vector<CalculatorInfo>& calculatorInfos, size_t targetCalculatorIndex)
{
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 收集与目标计算器相关的所有计算器和RegenDataId
    std::set<size_t> relevantCalculators;
    std::map<std::wstring, std::wstring> regenDataIdToNodeId;
    std::map<std::wstring, RegenDataId> keyToRegenDataId; // 缓存RegenDataId对象

    // 添加目标计算器
    relevantCalculators.insert(targetCalculatorIndex);

    // 查找上游计算器（产生目标计算器输入数据的计算器）
    for (const RegenDataId& inputDataId : calculatorInfos[targetCalculatorIndex].inputDataIds)
    {
        std::wstring inputKey = RegenDataIdToKey(inputDataId);
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (RegenDataIdToKey(calculatorInfos[i].outputDataId) == inputKey)
            {
                relevantCalculators.insert(i);
                break;
            }
        }
    }

    // 查找下游计算器（使用目标计算器输出数据的计算器）
    std::wstring targetOutputKey = RegenDataIdToKey(calculatorInfos[targetCalculatorIndex].outputDataId);
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        for (const RegenDataId& inputDataId : calculatorInfos[i].inputDataIds)
        {
            if (RegenDataIdToKey(inputDataId) == targetOutputKey)
            {
                relevantCalculators.insert(i);
                break;
            }
        }
    }

    // 收集所有相关的RegenDataId并缓存对象
    std::set<std::wstring> regenDataIdKeys;
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        regenDataIdKeys.insert(outputKey);
        keyToRegenDataId[outputKey] = info.outputDataId; // 缓存RegenDataId对象

        // 输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            regenDataIdKeys.insert(inputKey);
            keyToRegenDataId[inputKey] = inputDataId; // 缓存RegenDataId对象
        }
    }

    // 为RegenDataId分配节点ID
    size_t dataNodeCounter = 0;
    for (const std::wstring& key : regenDataIdKeys)
    {
        std::wstring nodeId = L"data_" + std::to_wstring(dataNodeCounter++);
        regenDataIdToNodeId[key] = nodeId;
    }

    // 按图元分组生成subgraph
    std::map<ElementId, std::vector<size_t>> elementGroups;
    for (size_t i : relevantCalculators)
    {
        ElementId elemId = calculatorInfos[i].pElement->GetElementId();
        elementGroups[elemId].push_back(i);
    }

    size_t elementIndex = 0;
    for (const auto& group : elementGroups)
    {
        ElementId elemId = group.first;
        const std::vector<size_t>& calculators = group.second;

        std::wstring elementTypeName = GetElementTypeName(calculatorInfos[calculators[0]].pElement);
        ss << L"    subgraph Element" << elementIndex << L"[\"" << elementTypeName << L":" << elemId.AsInt64() << L"\"]\n";

        for (size_t calcIndex : calculators)
        {
            const CalculatorInfo& info = calculatorInfos[calcIndex];
            std::wstring calcNodeId = L"calc" + std::to_wstring(calcIndex);
            ss << L"        " << calcNodeId << L"[\"" << info.calculatorName << L"\"]\n";
        }

        ss << L"    end\n\n";
        elementIndex++;
    }

    // 生成RegenDataId节点
    for (const auto& pair : regenDataIdToNodeId)
    {
        const std::wstring& key = pair.first;
        const std::wstring& nodeId = pair.second;

        // 从缓存中获取RegenDataId对象
        auto regenDataIt = keyToRegenDataId.find(key);
        if (regenDataIt != keyToRegenDataId.end())
        {
            std::wstring readableInfo = GetRegenDataIdReadableInfo(regenDataIt->second);
            ss << L"    " << nodeId << L"[\"" << readableInfo << L"\"]\n";
        }
    }

    ss << L"\n";

    // 生成连接
    std::set<std::wstring> connections;
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring calcNodeId = L"calc" + std::to_wstring(i);
        bool isTargetCalculator = (i == targetCalculatorIndex);

        // 计算器 -> 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        auto outputNodeIt = regenDataIdToNodeId.find(outputKey);
        if (outputNodeIt != regenDataIdToNodeId.end())
        {
            std::wstring arrow = isTargetCalculator ? L" ==> " : L" --> ";
            std::wstring connection = calcNodeId + arrow + outputNodeIt->second;
            if (connections.find(connection) == connections.end())
            {
                ss << L"    " << connection << L"\n";
                connections.insert(connection);
            }
        }

        // 输入RegenDataId -> 计算器
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            auto inputNodeIt = regenDataIdToNodeId.find(inputKey);
            if (inputNodeIt != regenDataIdToNodeId.end())
            {
                std::wstring arrow = isTargetCalculator ? L" ==> " : L" --> ";
                std::wstring connection = inputNodeIt->second + arrow + calcNodeId;
                if (connections.find(connection) == connections.end())
                {
                    ss << L"    " << connection << L"\n";
                    connections.insert(connection);
                }
            }
        }
    }

    // 应用样式（简化版，只突出目标计算器）
    ss << L"\n    %% 样式定义\n";
    ss << L"    classDef targetCalculatorStyle fill:#ffcdd2,stroke:#e57373,stroke-width:3px,color:#333,font-weight:bold\n";
    ss << L"    classDef normalCalculatorStyle fill:#f8f9fa,stroke:#dee2e6,stroke-width:1px,color:#6c757d\n";
    ss << L"    classDef dataStyle fill:#e3f2fd,stroke:#42a5f5,stroke-width:1px,color:#1565c0\n";

    // 应用样式到节点
    for (size_t i : relevantCalculators)
    {
        std::wstring calcNodeId = L"calc" + std::to_wstring(i);
        if (i == targetCalculatorIndex)
        {
            ss << L"    class " << calcNodeId << L" targetCalculatorStyle\n";
        }
        else
        {
            ss << L"    class " << calcNodeId << L" normalCalculatorStyle\n";
        }
    }

    for (const auto& pair : regenDataIdToNodeId)
    {
        const std::wstring& nodeId = pair.second;
        ss << L"    class " << nodeId << L" dataStyle\n";
    }

    return ss.str();
}

void CmdCalculatorGraphAnalyzer::AnalyzeElementCalculators(IDocument* pDoc, std::vector<CalculatorInfo>& calculatorInfos)
{
    // 获取文档中的所有图元
    std::vector<IElement*> allElements = pDoc->GetAllElements();

    for (const IElement* pElement : allElements)
    {
        if (!pElement)
            continue;

        // 获取图元的关联更新组件
        const IElementRegenerationComponent* pRegenComponent = pElement->GetElementRegenerationComponent();
        if (!pRegenComponent || !pRegenComponent->CanHaveCalculators())
            continue;

        // 创建计算器收集器
        OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pDoc);
        if (!opCalculators)
            continue;

        // 获取图元的所有计算器
        pRegenComponent->GetCalculators(opCalculators.get());

        // 遍历计算器
        int calculatorCount = opCalculators->GetCalculatorCount();
        for (int i = 0; i < calculatorCount; i++)
        {
            const ICalculator* pCalculator = opCalculators->GetCalcualtorByIndex(i);
            if (!pCalculator)
                continue;

            CalculatorInfo info;
            info.calculatorName = pCalculator->GetCalculatorName();
            info.elementId = std::to_wstring(pElement->GetElementId().AsInt64());
            info.elementType = GetElementTypeName(pElement);
            info.outputDataId = pCalculator->GetRegenDataId();
            info.outputDataId.ObjectId = pElement->GetElementId().AsInt64();
            info.pElement = pElement;  // 保存图元指针用于分组

            // 获取输入数据ID
            pCalculator->ReportInputDataIds(info.inputDataIds);

            calculatorInfos.push_back(info);
        }
    }
}

std::wstring CmdCalculatorGraphAnalyzer::GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 建立图元之间的连接关系（通过RegenDataId）
    std::map<ElementId, std::vector<ElementId>> elementConnections;

    // 建立图元之间的连接关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        ElementId currentElementId = info.pElement->GetElementId();

        // 检查每个输入RegenDataId的ObjectId对应的图元
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            // 通过ObjectId找到对应的图元
            ElementId inputElementId(inputDataId.ObjectId);

            // 如果输入RegenDataId的图元与当前计算器的图元不同，建立连接
            if (inputElementId != currentElementId)
            {
                // 建立双向连接，避免重复
                auto& currentConnections = elementConnections[currentElementId];
                auto& inputConnections = elementConnections[inputElementId];

                if (std::find(currentConnections.begin(), currentConnections.end(), inputElementId) == currentConnections.end())
                {
                    currentConnections.push_back(inputElementId);
                }
                if (std::find(inputConnections.begin(), inputConnections.end(), currentElementId) == inputConnections.end())
                {
                    inputConnections.push_back(currentElementId);
                }
            }
        }
    }

    // 使用DFS找到连通的图元组
    std::set<ElementId> visited;
    std::vector<std::vector<ElementId>> connectedElementGroups;

    for (const auto& elementConnection : elementConnections)
    {
        ElementId elementId = elementConnection.first;
        if (visited.find(elementId) == visited.end())
        {
            std::vector<ElementId> group;
            DFSFindElementGroup(elementId, elementConnections, visited, group);

            // 只有包含多个图元的组才加入分组（图元之间有连接）
            if (group.size() > 1)
            {
                connectedElementGroups.push_back(group);
            }
        }
    }

    // 如果指定了目标ElementId，进行有向图过滤
    std::set<ElementId> reachableElements;
    std::set<ElementId> connectedElements; // 真正有连接关系的图元
    std::set<size_t> calculatorsOnPath; // 在通往目标图元路径上的计算器
    if (m_targetElementId.IsValid())
    {
        // 找到与目标ElementId有数据流关系的所有图元
        FindReachableElements(calculatorInfos, m_targetElementId, reachableElements);

        // 进一步过滤：只保留真正有连接关系的图元
        FindConnectedElements(calculatorInfos, m_targetElementId, connectedElements);

        // 找到在通往目标图元路径上的计算器
        FindCalculatorsOnPathToTarget(calculatorInfos, m_targetElementId, calculatorsOnPath);

        // 过滤连通组，只保留有连接关系的图元
        std::vector<std::vector<ElementId>> filteredGroups;
        for (const auto& group : connectedElementGroups)
        {
            std::vector<ElementId> filteredGroup;
            for (const ElementId& elementId : group)
            {
                if (connectedElements.find(elementId) != connectedElements.end())
                {
                    filteredGroup.push_back(elementId);
                }
            }
            // 只要过滤后的组不为空就保留
            if (!filteredGroup.empty())
            {
                filteredGroups.push_back(filteredGroup);
            }
        }
        connectedElementGroups = filteredGroups;
    }

    // 按Element分组计算器
    std::map<ElementId, std::vector<size_t>> elementGroups;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        elementGroups[calculatorInfos[i].pElement->GetElementId()].push_back(i);
    }

    // 收集相关计算器的RegenDataId
    std::set<std::wstring> allRegenDataIds;
    std::set<size_t> relevantCalculators;

    // 首先收集所有相关的计算器索引
    if (m_onlyMultipleElements)
    {
        // 只收集在组中的计算器
        for (const auto& connectedElementGroup : connectedElementGroups)
        {
            for (const ElementId& elementId : connectedElementGroup)
            {
                for (size_t i = 0; i < calculatorInfos.size(); i++)
                {
                    if (calculatorInfos[i].pElement->GetElementId() == elementId)
                    {
                        // 如果指定了目标ElementId，只收集在路径上的计算器
                        if (m_targetElementId.IsValid())
                        {
                            if (calculatorsOnPath.find(i) != calculatorsOnPath.end())
                            {
                                relevantCalculators.insert(i);
                            }
                        }
                        else
                        {
                            relevantCalculators.insert(i);
                        }
                    }
                }
            }
        }
    }
    else
    {
        // 收集所有计算器，但如果指定了目标ElementId，则只收集在路径上的计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            ElementId calcElementId = calculatorInfos[i].pElement->GetElementId();

            // 如果指定了目标ElementId，只收集在路径上的计算器
            if (m_targetElementId.IsValid())
            {
                if (calculatorsOnPath.find(i) != calculatorsOnPath.end())
                {
                    relevantCalculators.insert(i);
                }
            }
            else
            {
                relevantCalculators.insert(i);
            }
        }
    }

    // 收集相关计算器的RegenDataId
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 收集输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        allRegenDataIds.insert(outputKey);

        // 收集输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            allRegenDataIds.insert(inputKey);
        }
    }

    // 创建图元分组结构：直接显示图元 -> 计算器
    size_t elementIndex = 0;
    std::set<ElementId> processedElements;

    // 处理连通的图元组
    for (const auto& connectedElementGroup : connectedElementGroups)
    {
        for (const ElementId& elementId : connectedElementGroup)
        {
            processedElements.insert(elementId);

            // 找到属于这个图元的所有相关计算器
            std::vector<size_t> calculatorIndices;
            for (size_t i = 0; i < calculatorInfos.size(); i++)
            {
                if (calculatorInfos[i].pElement->GetElementId() == elementId &&
                    relevantCalculators.find(i) != relevantCalculators.end())
                {
                    calculatorIndices.push_back(i);
                }
            }

            if (!calculatorIndices.empty())
            {
                std::wstring elementIdStr = std::to_wstring(elementId.AsInt64());
                std::wstring elementType = GetElementTypeName(calculatorInfos[calculatorIndices[0]].pElement);

                // 创建图元子图
                std::wstring subgraphId = L"Element" + std::to_wstring(elementIndex);
                ss << L"    subgraph " << subgraphId
                   << L"[\"" << elementType << L":" << elementIdStr << L"\"]\n";

                // 在图元子图中创建计算器节点
                for (size_t calcIndex : calculatorIndices)
                {
                    const CalculatorInfo& info = calculatorInfos[calcIndex];
                    std::wstring nodeId = L"calc" + std::to_wstring(calcIndex);

                    ss << L"        " << nodeId << L"[\"" << info.calculatorName << L"\"]\n";
                }

                ss << L"    end\n";
                elementIndex++;
            }
        }
    }

    // 处理孤立的图元（没有跨图元连接的图元）
    if (!m_onlyMultipleElements)
    {
        for (const auto& elementGroup : elementGroups)
        {
            ElementId elementId = elementGroup.first;
            const std::vector<size_t>& calculatorIndices = elementGroup.second;

            // 检查这个图元是否已经被处理（在某个组中）
            if (processedElements.find(elementId) == processedElements.end())
            {
                // 如果指定了目标ElementId，只处理有连接关系的图元
                if (m_targetElementId.IsValid() && connectedElements.find(elementId) == connectedElements.end())
                {
                    continue;
                }

                // 找到属于这个图元的所有相关计算器
                std::vector<size_t> relevantCalculatorIndices;
                for (size_t calcIndex : calculatorIndices)
                {
                    if (relevantCalculators.find(calcIndex) != relevantCalculators.end())
                    {
                        relevantCalculatorIndices.push_back(calcIndex);
                    }
                }

                if (!relevantCalculatorIndices.empty())
                {
                    std::wstring elementIdStr = std::to_wstring(elementId.AsInt64());
                    std::wstring elementType = GetElementTypeName(calculatorInfos[relevantCalculatorIndices[0]].pElement);

                    // 创建独立的图元子图
                    std::wstring subgraphId = L"Element" + std::to_wstring(elementIndex);
                    ss << L"    subgraph " << subgraphId << L"[\"" << elementType << L":" << elementIdStr << L"\"]\n";

                    for (size_t calcIndex : relevantCalculatorIndices)
                    {
                        const CalculatorInfo& info = calculatorInfos[calcIndex];
                        std::wstring nodeId = L"calc" + std::to_wstring(calcIndex);

                        ss << L"        " << nodeId << L"[\"" << info.calculatorName << L"\"]\n";
                    }

                    ss << L"    end\n";
                    elementIndex++;
                }
            }
        }
    }

    // 创建RegenDataId节点ID映射和实际RegenDataId对象的映射
    std::map<std::wstring, std::wstring> regenDataIdToNodeId;
    std::map<std::wstring, RegenDataId> keyToRegenDataId;

    // 建立key到RegenDataId对象的映射
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        keyToRegenDataId[outputKey] = info.outputDataId;

        // 输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            keyToRegenDataId[inputKey] = inputDataId;
        }
    }

    // 过滤掉没有连接到任何相关计算器的RegenDataId
    std::set<std::wstring> connectedRegenDataIds;
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 收集输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        connectedRegenDataIds.insert(outputKey);

        // 收集输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            connectedRegenDataIds.insert(inputKey);
        }
    }

    // 进一步过滤：移除孤立的RegenDataId（没有连接到任何相关计算器的RegenDataId）
    std::set<std::wstring> finalRegenDataIds;
    for (const std::wstring& regenDataKey : connectedRegenDataIds)
    {
        bool isConnected = false;

        // 检查是否有相关计算器输出这个RegenDataId
        for (size_t i : relevantCalculators)
        {
            const CalculatorInfo& info = calculatorInfos[i];
            std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
            if (outputKey == regenDataKey)
            {
                isConnected = true;
                break;
            }
        }

        // 检查是否有相关计算器使用这个RegenDataId作为输入
        if (!isConnected)
        {
            for (size_t i : relevantCalculators)
            {
                const CalculatorInfo& info = calculatorInfos[i];
                for (const RegenDataId& inputDataId : info.inputDataIds)
                {
                    std::wstring inputKey = RegenDataIdToKey(inputDataId);
                    if (inputKey == regenDataKey)
                    {
                        isConnected = true;
                        break;
                    }
                }
                if (isConnected) break;
            }
        }

        if (isConnected)
        {
            finalRegenDataIds.insert(regenDataKey);
        }
    }

    connectedRegenDataIds = finalRegenDataIds;

    size_t dataNodeIndex = 0;
    for (const std::wstring& regenDataKey : connectedRegenDataIds)
    {
        std::wstring nodeId = L"data_" + std::to_wstring(dataNodeIndex);
        regenDataIdToNodeId[regenDataKey] = nodeId;
        dataNodeIndex++;

        // 使用可读信息显示RegenDataId节点
        auto it = keyToRegenDataId.find(regenDataKey);
        std::wstring displayInfo;
        if (it != keyToRegenDataId.end())
        {
            displayInfo = GetRegenDataIdReadableInfo(it->second);
        }
        else
        {
            // 如果找不到对应的RegenDataId对象，使用原始格式
            displayInfo = regenDataKey;
            size_t pos = 0;
            while ((pos = displayInfo.find(L"_", pos)) != std::wstring::npos) {
                if (pos == displayInfo.find(L"_")) {
                    displayInfo.replace(pos, 1, L",Data:");
                    pos += 6;
                } else {
                    displayInfo.replace(pos, 1, L",Ext:");
                    pos += 5;
                }
            }
            displayInfo = L"Obj:" + displayInfo;
        }

        ss << L"    " << nodeId << L"[\"" << displayInfo << L"\"]\n";
    }

    // 创建连接：计算器 -> 输出RegenDataId -> 输入计算器
    std::set<std::wstring> connections;

    // 收集目标图元的所有计算器
    std::set<size_t> targetCalculators;
    for (size_t i : relevantCalculators)
    {
        if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
        {
            targetCalculators.insert(i);
        }
    }

    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring calcNodeId = L"calc" + std::to_wstring(i);
        bool isTargetCalculator = (targetCalculators.find(i) != targetCalculators.end());

        // 计算器 -> 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        auto outputNodeIt = regenDataIdToNodeId.find(outputKey);
        if (outputNodeIt != regenDataIdToNodeId.end())
        {
            // 检查这个输出是否被目标计算器使用
            bool isUsedByTarget = false;
            for (size_t targetCalc : targetCalculators)
            {
                for (const RegenDataId& targetInputDataId : calculatorInfos[targetCalc].inputDataIds)
                {
                    if (RegenDataIdToKey(targetInputDataId) == outputKey)
                    {
                        isUsedByTarget = true;
                        break;
                    }
                }
                if (isUsedByTarget) break;
            }

            // 如果是目标计算器的输出或被目标计算器使用，使用加重箭头
            std::wstring arrow = (isTargetCalculator || isUsedByTarget) ? L" ==> " : L" --> ";
            std::wstring connection = calcNodeId + arrow + outputNodeIt->second;
            if (connections.find(connection) == connections.end())
            {
                ss << L"    " << connection << L"\n";
                connections.insert(connection);
            }
        }

        // 输入RegenDataId -> 计算器
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            auto inputNodeIt = regenDataIdToNodeId.find(inputKey);
            if (inputNodeIt != regenDataIdToNodeId.end())
            {
                // 检查这个RegenDataId是否来自目标计算器
                bool isFromTargetCalculator = false;
                for (size_t targetCalc : targetCalculators)
                {
                    std::wstring targetOutputKey = RegenDataIdToKey(calculatorInfos[targetCalc].outputDataId);
                    if (targetOutputKey == inputKey)
                    {
                        isFromTargetCalculator = true;
                        break;
                    }
                }

                // 如果输入到目标计算器或来自目标计算器，使用加重箭头
                std::wstring arrow = (isTargetCalculator || isFromTargetCalculator) ? L" ==> " : L" --> ";
                std::wstring connection = inputNodeIt->second + arrow + calcNodeId;
                if (connections.find(connection) == connections.end())
                {
                    ss << L"    " << connection << L"\n";
                    connections.insert(connection);
                }
            }
        }
    }

    // 添加样式定义
    if (m_targetElementId.IsValid())
    {
        ss << L"\n    %% 样式定义\n";

        // 定义10种淡雅明亮且容易区分的颜色用于目标图元的计算器
        std::vector<std::wstring> calculatorColors = {
            L"fill:#ffcdd2,stroke:#e57373,stroke-width:2px,color:#333", // 淡雅红色
            L"fill:#ffe0b2,stroke:#ffb74d,stroke-width:2px,color:#333", // 淡雅橙色
            L"fill:#fff9c4,stroke:#fff176,stroke-width:2px,color:#333", // 淡雅黄色
            L"fill:#c8e6c9,stroke:#81c784,stroke-width:2px,color:#333", // 淡雅绿色
            L"fill:#b2dfdb,stroke:#4db6ac,stroke-width:2px,color:#333", // 淡雅青色
            L"fill:#bbdefb,stroke:#64b5f6,stroke-width:2px,color:#333", // 淡雅蓝色
            L"fill:#e1bee7,stroke:#ba68c8,stroke-width:2px,color:#333", // 淡雅紫色
            L"fill:#f8bbd9,stroke:#f06292,stroke-width:2px,color:#333", // 淡雅粉色
            L"fill:#b2ebf2,stroke:#4dd0e1,stroke-width:2px,color:#333", // 淡雅天蓝色
            L"fill:#d7ccc8,stroke:#a1887f,stroke-width:2px,color:#333"  // 淡雅棕色
        };

        // 目标图元样式（淡雅明亮的绿色）
        ss << L"    classDef targetElementStyle fill:#a5d6a7,stroke:#66bb6a,stroke-width:3px,color:#2e7d32,font-weight:bold\n";

        // 为每种计算器颜色定义样式
        for (size_t i = 0; i < calculatorColors.size(); i++)
        {
            ss << L"    classDef calcStyle" << i << L" " << calculatorColors[i] << L"\n";
            ss << L"    classDef dataStyle" << i << L" " << calculatorColors[i] << L"\n";
        }

        // 白色样式（多连接计算器）
        ss << L"    classDef whiteStyle fill:#fafafa,stroke:#9e9e9e,stroke-width:2px,color:#424242\n";

        // 灰色样式（其他节点）
        ss << L"    classDef grayStyle fill:#f8f9fa,stroke:#dee2e6,stroke-width:1px,color:#6c757d\n";

        // 应用样式到节点
        ApplyNodeStyles(ss, calculatorInfos, relevantCalculators, regenDataIdToNodeId, calculatorColors, elementIndex);

        // 应用边颜色
        ApplyEdgeStyles(ss, calculatorInfos, relevantCalculators, regenDataIdToNodeId);
    }
    else
    {
        // 没有指定目标ElementId时，使用默认样式
        ss << L"\n    %% 默认样式\n";
        ss << L"    classDef defaultStyle fill:#e3f2fd,stroke:#42a5f5,stroke-width:2px,color:#1565c0\n";

        for (const auto& pair : regenDataIdToNodeId)
        {
            const std::wstring& nodeId = pair.second;
            ss << L"    class " << nodeId << L" defaultStyle\n";
        }
    }

    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::RegenDataIdToString(const RegenDataId& dataId)
{
    std::wstringstream ss;
    ss << L"Obj:" << dataId.ObjectId << L",Data:" << dataId.DataId;
    if (dataId.ExtendedId != -1)
    {
        ss << L",Ext:" << dataId.ExtendedId;
    }
    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::RegenDataIdToKey(const RegenDataId& dataId)
{
    std::wstringstream ss;
    ss << dataId.ObjectId << L"_" << dataId.DataId << L"_" << dataId.ExtendedId;
    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::GetRegenDataIdReadableInfo(const RegenDataId& dataId)
{
    std::wstringstream ss;

    IDocument* pDocument = UiDocumentViewUtils::GetCurrentDocument();
    IElement* pElement = pDocument->GetElement(ElementId(dataId.ObjectId));
    if (pElement)
    {
        ss << GetElementTypeName(pElement) << L"#" << dataId.ObjectId;
    }
    else
    {
        ss << L"nullptr:" << dataId.ObjectId;
    }

    // 获取可读的DataId信息
    const gcmp::RegenDataIdInfo& dataIdInfo = gcmp::RegenDataIdManager::Get().GetRegenDataIdInfo((int)dataId.DataId);
    if (dataIdInfo.Id != -1 && !dataIdInfo.Name.empty())
    {
        // 如果有可读信息，显示名称和类名
        ss << L",<br/>Data:";
        if (!dataIdInfo.ClassName.empty())
        {
            ss << dataIdInfo.ClassName << L"#";
        }
        ss << dataIdInfo.Name;
    }
    else
    {
        // 如果没有可读信息，显示原始DataId
        ss << L",<br/>Data:" << dataId.DataId;
    }

    if (dataId.ExtendedId != -1)
    {
        ss << L",<br/>Ext:" << dataId.ExtendedId;
    }
    std::wstring rslt = ss.str();
    StringUtil::ReplaceAll(rslt, L"\"\"", L"::");
    StringUtil::ReplaceAll(rslt, L"\"", L"");
    StringUtil::ReplaceAll(rslt, L" + ", L"::");
    return rslt;
}

void CmdCalculatorGraphAnalyzer::DFSFindElementGroup(const ElementId& elementId, const std::map<ElementId, std::vector<ElementId>>& connections,
                                                   std::set<ElementId>& visited, std::vector<ElementId>& group)
{
    visited.insert(elementId);
    group.push_back(elementId);

    // 遍历所有连接的图元
    auto it = connections.find(elementId);
    if (it != connections.end())
    {
        for (const ElementId& connectedElementId : it->second)
        {
            if (visited.find(connectedElementId) == visited.end())
            {
                DFSFindElementGroup(connectedElementId, connections, visited, group);
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindReachableElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                      std::set<ElementId>& reachableElements)
{
    // 首先添加目标ElementId本身
    reachableElements.insert(targetElementId);

    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators; // RegenDataId -> 输出该数据的计算器列表
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;  // 计算器索引 -> 输入的RegenDataId列表
    std::map<size_t, std::wstring> calculatorToOutput;              // 计算器索引 -> 输出的RegenDataId

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用BFS向前追溯（找输入链）和向后追溯（找输出链）
    std::queue<ElementId> toProcess;
    toProcess.push(targetElementId);

    while (!toProcess.empty())
    {
        ElementId currentElementId = toProcess.front();
        toProcess.pop();

        // 找到属于当前图元的所有计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (calculatorInfos[i].pElement->GetElementId() == currentElementId)
            {
                // 向前追溯：找到为当前计算器提供输入的图元
                for (const std::wstring& inputKey : calculatorToInputs[i])
                {
                    auto it = outputToCalculators.find(inputKey);
                    if (it != outputToCalculators.end())
                    {
                        for (size_t sourceCalcIndex : it->second)
                        {
                            ElementId sourceElementId = calculatorInfos[sourceCalcIndex].pElement->GetElementId();
                            if (reachableElements.find(sourceElementId) == reachableElements.end())
                            {
                                reachableElements.insert(sourceElementId);
                                toProcess.push(sourceElementId);
                            }
                        }
                    }
                }

                // 向后追溯：找到使用当前计算器输出的图元
                std::wstring outputKey = calculatorToOutput[i];
                for (size_t j = 0; j < calculatorInfos.size(); j++)
                {
                    for (const std::wstring& inputKey : calculatorToInputs[j])
                    {
                        if (inputKey == outputKey)
                        {
                            ElementId targetCalcElementId = calculatorInfos[j].pElement->GetElementId();
                            if (reachableElements.find(targetCalcElementId) == reachableElements.end())
                            {
                                reachableElements.insert(targetCalcElementId);
                                toProcess.push(targetCalcElementId);
                            }
                        }
                    }
                }
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindConnectedElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                      std::set<ElementId>& connectedElements)
{
    // 添加目标ElementId本身
    connectedElements.insert(targetElementId);

    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators;
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;
    std::map<size_t, std::wstring> calculatorToOutput;

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用BFS找到所有有连接关系的图元
    std::queue<ElementId> toProcess;
    toProcess.push(targetElementId);

    while (!toProcess.empty())
    {
        ElementId currentElementId = toProcess.front();
        toProcess.pop();

        // 找到属于当前图元的所有计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (calculatorInfos[i].pElement->GetElementId() == currentElementId)
            {
                // 向前追溯：找到为当前计算器提供输入的图元
                for (const std::wstring& inputKey : calculatorToInputs[i])
                {
                    auto it = outputToCalculators.find(inputKey);
                    if (it != outputToCalculators.end())
                    {
                        for (size_t sourceCalcIndex : it->second)
                        {
                            ElementId sourceElementId = calculatorInfos[sourceCalcIndex].pElement->GetElementId();
                            if (connectedElements.find(sourceElementId) == connectedElements.end())
                            {
                                connectedElements.insert(sourceElementId);
                                toProcess.push(sourceElementId);
                            }
                        }
                    }
                }

                // 向后追溯：找到使用当前计算器输出的图元
                std::wstring outputKey = calculatorToOutput[i];
                for (size_t j = 0; j < calculatorInfos.size(); j++)
                {
                    for (const std::wstring& inputKey : calculatorToInputs[j])
                    {
                        if (inputKey == outputKey)
                        {
                            ElementId targetCalcElementId = calculatorInfos[j].pElement->GetElementId();
                            if (connectedElements.find(targetCalcElementId) == connectedElements.end())
                            {
                                connectedElements.insert(targetCalcElementId);
                                toProcess.push(targetCalcElementId);
                            }
                        }
                    }
                }
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindCalculatorsOnPathToTarget(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                             std::set<size_t>& calculatorsOnPath)
{
    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators; // RegenDataId -> 输出该数据的计算器列表
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;  // 计算器索引 -> 输入的RegenDataId列表
    std::map<size_t, std::wstring> calculatorToOutput;              // 计算器索引 -> 输出的RegenDataId

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用两阶段算法：
    // 1. 从目标图元向上游追溯，找到所有能影响目标图元的计算器
    // 2. 从目标图元向下游追溯，找到所有受目标图元影响的计算器

    std::set<size_t> upstreamCalculators;   // 上游计算器（影响目标图元的）
    std::set<size_t> downstreamCalculators; // 下游计算器（受目标图元影响的）

    // 首先添加目标图元的所有计算器
    std::set<size_t> targetCalculators;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (calculatorInfos[i].pElement->GetElementId() == targetElementId)
        {
            targetCalculators.insert(i);
            calculatorsOnPath.insert(i);
        }
    }

    // 第一阶段：向上游追溯（找到能影响目标图元的计算器）
    std::queue<size_t> upstreamQueue;
    for (size_t targetCalc : targetCalculators)
    {
        upstreamQueue.push(targetCalc);
    }

    while (!upstreamQueue.empty())
    {
        size_t currentCalcIndex = upstreamQueue.front();
        upstreamQueue.pop();

        // 向前追溯：找到为当前计算器提供输入的计算器
        for (const std::wstring& inputKey : calculatorToInputs[currentCalcIndex])
        {
            auto it = outputToCalculators.find(inputKey);
            if (it != outputToCalculators.end())
            {
                for (size_t sourceCalcIndex : it->second)
                {
                    if (upstreamCalculators.find(sourceCalcIndex) == upstreamCalculators.end())
                    {
                        upstreamCalculators.insert(sourceCalcIndex);
                        upstreamQueue.push(sourceCalcIndex);
                    }
                }
            }
        }
    }

    // 第二阶段：向下游追溯（找到受目标图元影响的计算器）
    std::queue<size_t> downstreamQueue;
    for (size_t targetCalc : targetCalculators)
    {
        downstreamQueue.push(targetCalc);
    }

    while (!downstreamQueue.empty())
    {
        size_t currentCalcIndex = downstreamQueue.front();
        downstreamQueue.pop();

        // 向后追溯：找到使用当前计算器输出的计算器
        std::wstring outputKey = calculatorToOutput[currentCalcIndex];
        for (size_t j = 0; j < calculatorInfos.size(); j++)
        {
            for (const std::wstring& inputKey : calculatorToInputs[j])
            {
                if (inputKey == outputKey)
                {
                    if (downstreamCalculators.find(j) == downstreamCalculators.end())
                    {
                        downstreamCalculators.insert(j);
                        downstreamQueue.push(j);
                    }
                }
            }
        }
    }

    // 第三阶段：验证路径有效性，只保留真正有直接路径连接的计算器
    std::set<size_t> validCalculators;

    // 添加目标计算器
    for (size_t calc : targetCalculators)
    {
        validCalculators.insert(calc);
    }

    // 验证上游计算器：必须有直接路径到达目标计算器
    for (size_t upstreamCalc : upstreamCalculators)
    {
        for (size_t targetCalc : targetCalculators)
        {
            std::set<size_t> visited;
            if (HasDirectPathBetweenCalculators(calculatorInfos, upstreamCalc, targetCalc, visited))
            {
                validCalculators.insert(upstreamCalc);
                break;
            }
        }
    }

    // 验证下游计算器：目标计算器必须有直接路径到达它们
    for (size_t downstreamCalc : downstreamCalculators)
    {
        for (size_t targetCalc : targetCalculators)
        {
            std::set<size_t> visited;
            if (HasDirectPathBetweenCalculators(calculatorInfos, targetCalc, downstreamCalc, visited))
            {
                validCalculators.insert(downstreamCalc);
                break;
            }
        }
    }

    // 最终结果
    calculatorsOnPath = validCalculators;
}

bool CmdCalculatorGraphAnalyzer::HasDirectPathBetweenCalculators(const std::vector<CalculatorInfo>& calculatorInfos,
                                                            size_t fromCalc, size_t toCalc, std::set<size_t>& visited)
{
    // 如果已经访问过这个计算器，避免无限循环
    if (visited.find(fromCalc) != visited.end())
    {
        return false;
    }

    // 如果到达目标计算器
    if (fromCalc == toCalc)
    {
        return true;
    }

    // 标记当前计算器为已访问
    visited.insert(fromCalc);

    // 建立输出到计算器的映射（仅用于当前路径查找）
    std::map<std::wstring, std::vector<size_t>> outputToCalculators;
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;

    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 获取当前计算器的输出
    const CalculatorInfo& fromInfo = calculatorInfos[fromCalc];
    std::wstring fromOutputKey = RegenDataIdToKey(fromInfo.outputDataId);

    // 查找使用当前计算器输出的所有计算器
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (i == fromCalc) continue; // 跳过自己

        for (const std::wstring& inputKey : calculatorToInputs[i])
        {
            if (inputKey == fromOutputKey)
            {
                // 找到直接连接的计算器，递归检查
                if (HasDirectPathBetweenCalculators(calculatorInfos, i, toCalc, visited))
                {
                    return true;
                }
            }
        }
    }

    // 移除访问标记，允许其他路径访问这个计算器
    visited.erase(fromCalc);
    return false;
}

void CmdCalculatorGraphAnalyzer::ApplyNodeStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                                               const std::set<size_t>& relevantCalculators,
                                               const std::map<std::wstring, std::wstring>& regenDataIdToNodeId,
                                               const std::vector<std::wstring>& calculatorColors, size_t totalElements)
{
    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators;
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;
    std::map<size_t, std::wstring> calculatorToOutput;

    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 收集目标图元的计算器
    std::vector<size_t> targetCalculators;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
        {
            targetCalculators.push_back(i);
        }
    }

    // 为目标图元的计算器分配颜色
    std::map<size_t, size_t> calculatorToColorIndex;
    for (size_t i = 0; i < targetCalculators.size(); i++)
    {
        size_t calcIndex = targetCalculators[i];
        size_t colorIndex = i % calculatorColors.size();
        calculatorToColorIndex[calcIndex] = colorIndex;
    }

    // 分析上下游计算器与目标计算器的连接关系
    std::map<size_t, std::set<size_t>> upstreamToTargetConnections; // 上游计算器 -> 连接的目标计算器集合
    std::map<size_t, std::set<size_t>> downstreamToTargetConnections; // 下游计算器 -> 连接的目标计算器集合

    for (size_t targetCalc : targetCalculators)
    {
        // 查找连接到目标计算器的上游计算器
        for (const std::wstring& inputKey : calculatorToInputs[targetCalc])
        {
            auto it = outputToCalculators.find(inputKey);
            if (it != outputToCalculators.end())
            {
                for (size_t sourceCalc : it->second)
                {
                    if (calculatorInfos[sourceCalc].pElement->GetElementId() != m_targetElementId)
                    {
                        upstreamToTargetConnections[sourceCalc].insert(targetCalc);
                    }
                }
            }
        }

        // 查找目标计算器连接到的下游计算器
        std::wstring outputKey = calculatorToOutput[targetCalc];
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (calculatorInfos[i].pElement->GetElementId() != m_targetElementId)
            {
                for (const std::wstring& inputKey : calculatorToInputs[i])
                {
                    if (inputKey == outputKey)
                    {
                        downstreamToTargetConnections[i].insert(targetCalc);
                    }
                }
            }
        }
    }

    ss << L"\n    %% 应用样式\n";

    // 应用目标图元subgraph样式
    std::set<ElementId> targetElementsInGraph;
    for (size_t i : relevantCalculators)
    {
        if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
        {
            targetElementsInGraph.insert(m_targetElementId);
            break;
        }
    }

    // 查找目标图元对应的Element索引并应用样式
    size_t elementIndex = 0;
    std::map<ElementId, size_t> elementToIndex;
    for (size_t i : relevantCalculators)
    {
        ElementId elemId = calculatorInfos[i].pElement->GetElementId();
        if (elementToIndex.find(elemId) == elementToIndex.end())
        {
            elementToIndex[elemId] = elementIndex;
            if (elemId == m_targetElementId)
            {
                ss << L"    class Element" << elementIndex << L" targetElementStyle\n";
            }
            elementIndex++;
        }
    }

    // 应用计算器样式
    for (size_t i : relevantCalculators)
    {
        std::wstring nodeId = L"calc" + std::to_wstring(i);

        if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
        {
            // 目标图元的计算器使用分配的颜色
            size_t colorIndex = calculatorToColorIndex[i];
            ss << L"    class " << nodeId << L" calcStyle" << colorIndex << L"\n";
        }
        else
        {
            // 上下游计算器
            bool isUpstream = upstreamToTargetConnections.find(i) != upstreamToTargetConnections.end();
            bool isDownstream = downstreamToTargetConnections.find(i) != downstreamToTargetConnections.end();

            if (isUpstream || isDownstream)
            {
                std::set<size_t> connectedTargets;
                if (isUpstream) connectedTargets = upstreamToTargetConnections[i];
                if (isDownstream) connectedTargets = downstreamToTargetConnections[i];

                if (connectedTargets.size() == 1)
                {
                    // 只连接一个目标计算器，使用相同颜色
                    size_t targetCalc = *connectedTargets.begin();
                    size_t colorIndex = calculatorToColorIndex[targetCalc];
                    ss << L"    class " << nodeId << L" calcStyle" << colorIndex << L"\n";
                }
                else
                {
                    // 连接多个目标计算器，使用白色
                    ss << L"    class " << nodeId << L" whiteStyle\n";
                }
            }
            else
            {
                // 其他计算器使用灰色
                ss << L"    class " << nodeId << L" grayStyle\n";
            }
        }
    }

    // 应用RegenDataId样式
    ApplyRegenDataIdStyles(ss, calculatorInfos, relevantCalculators, regenDataIdToNodeId,
                          calculatorToColorIndex, upstreamToTargetConnections, downstreamToTargetConnections);
}

void CmdCalculatorGraphAnalyzer::ApplyRegenDataIdStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                                                      const std::set<size_t>& relevantCalculators,
                                                      const std::map<std::wstring, std::wstring>& regenDataIdToNodeId,
                                                      const std::map<size_t, size_t>& calculatorToColorIndex,
                                                      const std::map<size_t, std::set<size_t>>& upstreamToTargetConnections,
                                                      const std::map<size_t, std::set<size_t>>& downstreamToTargetConnections)
{
    // 建立RegenDataId到计算器的映射
    std::map<std::wstring, size_t> regenDataToProducerCalc; // RegenDataId -> 产生它的计算器
    std::map<std::wstring, std::vector<size_t>> regenDataToConsumerCalcs; // RegenDataId -> 使用它的计算器列表

    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        regenDataToProducerCalc[outputKey] = i;

        // 输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            regenDataToConsumerCalcs[inputKey].push_back(i);
        }
    }

    // 为每个RegenDataId应用样式
    for (const auto& pair : regenDataIdToNodeId)
    {
        const std::wstring& regenDataKey = pair.first;
        const std::wstring& nodeId = pair.second;

        // 查找产生这个RegenDataId的计算器
        auto producerIt = regenDataToProducerCalc.find(regenDataKey);

        // 检查是否被目标计算器使用
        bool isUsedByTarget = false;
        size_t targetCalcUsingThis = 0;
        auto consumerIt = regenDataToConsumerCalcs.find(regenDataKey);
        if (consumerIt != regenDataToConsumerCalcs.end())
        {
            for (size_t consumerCalc : consumerIt->second)
            {
                if (calculatorInfos[consumerCalc].pElement->GetElementId() == m_targetElementId)
                {
                    isUsedByTarget = true;
                    targetCalcUsingThis = consumerCalc;
                    break;
                }
            }
        }

        if (producerIt != regenDataToProducerCalc.end())
        {
            size_t producerCalc = producerIt->second;

            if (calculatorInfos[producerCalc].pElement->GetElementId() == m_targetElementId)
            {
                // 来自目标图元计算器的RegenDataId，使用相同颜色
                size_t colorIndex = calculatorToColorIndex.at(producerCalc);
                ss << L"    class " << nodeId << L" dataStyle" << colorIndex << L"\n";
            }
            else if (isUsedByTarget)
            {
                // 被目标计算器使用的RegenDataId，使用目标计算器的颜色
                size_t colorIndex = calculatorToColorIndex.at(targetCalcUsingThis);
                ss << L"    class " << nodeId << L" dataStyle" << colorIndex << L"\n";
            }
            else
            {
                // 来自上下游计算器的RegenDataId
                bool isUpstream = upstreamToTargetConnections.find(producerCalc) != upstreamToTargetConnections.end();
                bool isDownstream = downstreamToTargetConnections.find(producerCalc) != downstreamToTargetConnections.end();

                if (isUpstream || isDownstream)
                {
                    std::set<size_t> connectedTargets;
                    if (isUpstream) connectedTargets = upstreamToTargetConnections.at(producerCalc);
                    if (isDownstream) connectedTargets = downstreamToTargetConnections.at(producerCalc);

                    if (connectedTargets.size() == 1)
                    {
                        // 只连接一个目标计算器，使用相同颜色
                        size_t targetCalc = *connectedTargets.begin();
                        size_t colorIndex = calculatorToColorIndex.at(targetCalc);
                        ss << L"    class " << nodeId << L" dataStyle" << colorIndex << L"\n";
                    }
                    else
                    {
                        // 连接多个目标计算器，使用白色
                        ss << L"    class " << nodeId << L" whiteStyle\n";
                    }
                }
                else
                {
                    // 其他RegenDataId使用灰色
                    ss << L"    class " << nodeId << L" grayStyle\n";
                }
            }
        }
        else if (isUsedByTarget)
        {
            // 没有找到产生者但被目标计算器使用，使用目标计算器的颜色
            size_t colorIndex = calculatorToColorIndex.at(targetCalcUsingThis);
            ss << L"    class " << nodeId << L" dataStyle" << colorIndex << L"\n";
        }
        else
        {
            // 没有找到产生者，可能是外部输入，使用灰色
            ss << L"    class " << nodeId << L" grayStyle\n";
        }
    }
}

void CmdCalculatorGraphAnalyzer::ApplyEdgeStyles(std::wstringstream& ss, const std::vector<CalculatorInfo>& calculatorInfos,
                                               const std::set<size_t>& relevantCalculators,
                                               const std::map<std::wstring, std::wstring>& regenDataIdToNodeId)
{
    ss << L"\n    %% 边样式说明\n";
    ss << L"    %% ==> 表示与目标图元相关的重要连接\n";
    ss << L"    %% --> 表示普通连接\n";
    ss << L"    %% 相同颜色的节点之间的边表示同一数据流\n";
}

std::wstring CmdCalculatorGraphAnalyzer::GetElementTypeName(const IElement* pElement)
{
    std::wstring rslt = StringUtil::ToWString(typeid(*pElement).name());
    StringUtil::ReplaceAll(rslt, L"class ", L"");
    return rslt;
}

REGISTER_COMMAND(CmdCalculatorGraphAnalyzer);

// CmdCalculatorGraphAnalyzerBySelection 实现
CmdCalculatorGraphAnalyzerBySelection::CmdCalculatorGraphAnalyzerBySelection()
    :CommandBase(ID_CMD_CALCULATOR_GRAPH_ANALYZER_BY_SELECTION, true)
{
}

gcmp::OwnerPtr<gcmp::IAction> CmdCalculatorGraphAnalyzerBySelection::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (!pDoc)
    {
        // 可以添加用户提示：没有打开的文档
        return nullptr;
    }

    // 获取当前选择
    ISelection* pSelection = UiDocumentViewUtils::GetCurrentSelection();
    if (!pSelection)
    {
        gcmp::UiCommonDialog::ShowMessageBox(L"错误", L"无法获取当前选择", (int)gcmp::UiCommonDialog::ButtonType::OK);
        return nullptr;
    }

    // 检查选择的图元数量
    std::vector<ElementId> selectedElements = pSelection->GetSelectedElementIds();
    if (selectedElements.empty())
    {
        gcmp::UiCommonDialog::ShowMessageBox(L"错误", L"请先选择一个图元", (int)gcmp::UiCommonDialog::ButtonType::OK);
        return nullptr;
    }
    else if (selectedElements.size() > 1)
    {
        gcmp::UiCommonDialog::ShowMessageBox(L"错误", L"请只选择一个图元，当前选择了" + std::to_wstring(selectedElements.size()) + L"个图元",
                                           (int)gcmp::UiCommonDialog::ButtonType::OK);
        return nullptr;
    }

    // 获取选中的图元ID
    ElementId selectedElementId = selectedElements[0];

    // 构造CommandParameters并调用原命令
    gcmp::CommandParameters params;
    params[L"ElementId"] = gcmp::CommandParameterValue::CreateInt64(selectedElementId.AsInt64());

    // 通过SendCommand调用原命令
    return gcmp::ICommandManager::Get()->SendCommand(ID_CMD_CALCULATOR_GRAPH_ANALYZER, params);
}

REGISTER_COMMAND(CmdCalculatorGraphAnalyzerBySelection);
