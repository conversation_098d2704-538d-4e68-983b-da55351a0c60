#include "CmdCalculatorGraphAnalyzer.h"
#include "ElemModule.h"
#include "ElemModuleCommandIds.h"
#include "CommandRegister.h"
#include "IDocument.h"
#include "IModelView.h"
#include "UiDocumentViewUtils.h"
#include "IUserTransaction.h"
#include "IApplicationWindow.h"
#include "IApplication.h"
#include "ICommandManager.h"
#include "IElement.h"
#include "IElementRegenerationComponent.h"
#include "ICalculatorCollection.h"
#include "ICalculator.h"
#include "RegenDataId.h"
#include "ElementId.h"
#include "UiCommonDialog.h"
#include "FileUtility.h"
#include "../RegenDataIdManager.h"
#include <sstream>
#include <fstream>
#include <set>
#include <map>
#include <queue>
#include <algorithm>
#include <Windows.h>
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

#define ID_CMD_CALCULATOR_GRAPH_ANALYZER L"CmdCalculatorGraphAnalyzer"

CmdCalculatorGraphAnalyzer::CmdCalculatorGraphAnalyzer()
    :CommandBase(ID_CMD_CALCULATOR_GRAPH_ANALYZER, true)
{
}

gcmp::OwnerPtr<gcmp::IAction> CmdCalculatorGraphAnalyzer::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (!pDoc)
    {
        // 可以添加用户提示：没有打开的文档
        return nullptr;
    }

    std::vector<CalculatorInfo> calculatorInfos;
    AnalyzeElementCalculators(pDoc, calculatorInfos);

    if (calculatorInfos.empty())
    {
        // 可以添加用户提示：没有找到计算器
        return nullptr;
    }

    std::wstring idStr;
    Int64 idNum = -1;
    if (UiCommonDialog::ShowInputBox(L"搜索视图", L"输入图元ElementId", L"", idStr) != UiCommonDialog::ButtonType::OK)
    {
    }

    idNum = StringUtil::ToNumber<Int64>(idStr);
    m_targetElementId = ElementId(idNum);

    std::wstring mermaidGraph = GenerateMermaidGraph(calculatorInfos);

    // 将Mermaid图保存到文件
    std::wstring filePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(GBMP_TR(L"请选择导出mermaid文件"),
        L"", GBMP_TR(L"markdown文件(*.mmd)"));

    // 使用UTF-8转换方式保存文件
    if (!filePathName.empty())
    {
        // 将wstring转换为UTF-8编码的string
        int utf8Size = WideCharToMultiByte(CP_UTF8, 0, mermaidGraph.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (utf8Size > 0)
        {
            std::vector<char> utf8Buffer(utf8Size);
            WideCharToMultiByte(CP_UTF8, 0, mermaidGraph.c_str(), -1, utf8Buffer.data(), utf8Size, nullptr, nullptr);

            // 去掉末尾的null终止符
            bool success = gcmp::FileUtility::Save(filePathName, utf8Buffer.data(), utf8Size - 1);
            if (success)
            {
                // 可以添加用户提示：文件保存成功
            }
            else
            {
                // 可以添加用户提示：文件保存失败
            }
        }
        else
        {
            // 可以添加用户提示：字符串转换失败
        }
    }
    else
    {
        // 可以添加用户提示：用户取消了文件保存
    }
    return nullptr;
}

void CmdCalculatorGraphAnalyzer::AnalyzeElementCalculators(IDocument* pDoc, std::vector<CalculatorInfo>& calculatorInfos)
{
    // 获取文档中的所有图元
    std::vector<IElement*> allElements = pDoc->GetAllElements();

    for (const IElement* pElement : allElements)
    {
        if (!pElement)
            continue;

        // 获取图元的关联更新组件
        const IElementRegenerationComponent* pRegenComponent = pElement->GetElementRegenerationComponent();
        if (!pRegenComponent || !pRegenComponent->CanHaveCalculators())
            continue;

        // 创建计算器收集器
        OwnerPtr<ICalculatorCollection> opCalculators = ICalculatorCollection::Create(pDoc);
        if (!opCalculators)
            continue;

        // 获取图元的所有计算器
        pRegenComponent->GetCalculators(opCalculators.get());

        // 遍历计算器
        int calculatorCount = opCalculators->GetCalculatorCount();
        for (int i = 0; i < calculatorCount; i++)
        {
            const ICalculator* pCalculator = opCalculators->GetCalcualtorByIndex(i);
            if (!pCalculator)
                continue;

            CalculatorInfo info;
            info.calculatorName = pCalculator->GetCalculatorName();
            info.elementId = std::to_wstring(pElement->GetElementId().AsInt64());
            info.elementType = GetElementTypeName(pElement);
            info.outputDataId = pCalculator->GetRegenDataId();
            info.outputDataId.ObjectId = pElement->GetElementId().AsInt64();
            info.pElement = pElement;  // 保存图元指针用于分组

            // 获取输入数据ID
            pCalculator->ReportInputDataIds(info.inputDataIds);

            calculatorInfos.push_back(info);
        }
    }
}

std::wstring CmdCalculatorGraphAnalyzer::GenerateMermaidGraph(const std::vector<CalculatorInfo>& calculatorInfos)
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    std::wstringstream ss;
    ss << L"graph TD\n";

    // 建立图元之间的连接关系（通过RegenDataId）
    std::map<ElementId, std::vector<ElementId>> elementConnections;

    // 建立图元之间的连接关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        ElementId currentElementId = info.pElement->GetElementId();

        // 检查每个输入RegenDataId的ObjectId对应的图元
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            // 通过ObjectId找到对应的图元
            ElementId inputElementId(inputDataId.ObjectId);

            // 如果输入RegenDataId的图元与当前计算器的图元不同，建立连接
            if (inputElementId != currentElementId)
            {
                // 建立双向连接，避免重复
                auto& currentConnections = elementConnections[currentElementId];
                auto& inputConnections = elementConnections[inputElementId];

                if (std::find(currentConnections.begin(), currentConnections.end(), inputElementId) == currentConnections.end())
                {
                    currentConnections.push_back(inputElementId);
                }
                if (std::find(inputConnections.begin(), inputConnections.end(), currentElementId) == inputConnections.end())
                {
                    inputConnections.push_back(currentElementId);
                }
            }
        }
    }

    // 使用DFS找到连通的图元组
    std::set<ElementId> visited;
    std::vector<std::vector<ElementId>> connectedElementGroups;

    for (const auto& elementConnection : elementConnections)
    {
        ElementId elementId = elementConnection.first;
        if (visited.find(elementId) == visited.end())
        {
            std::vector<ElementId> group;
            DFSFindElementGroup(elementId, elementConnections, visited, group);

            // 只有包含多个图元的组才加入分组（图元之间有连接）
            if (group.size() > 1)
            {
                connectedElementGroups.push_back(group);
            }
        }
    }

    // 如果指定了目标ElementId，进行有向图过滤
    std::set<ElementId> reachableElements;
    std::set<ElementId> connectedElements; // 真正有连接关系的图元
    std::set<size_t> calculatorsOnPath; // 在通往目标图元路径上的计算器
    if (m_targetElementId.IsValid())
    {
        // 找到与目标ElementId有数据流关系的所有图元
        FindReachableElements(calculatorInfos, m_targetElementId, reachableElements);

        // 进一步过滤：只保留真正有连接关系的图元
        FindConnectedElements(calculatorInfos, m_targetElementId, connectedElements);

        // 找到在通往目标图元路径上的计算器
        FindCalculatorsOnPathToTarget(calculatorInfos, m_targetElementId, calculatorsOnPath);

        // 过滤连通组，只保留有连接关系的图元
        std::vector<std::vector<ElementId>> filteredGroups;
        for (const auto& group : connectedElementGroups)
        {
            std::vector<ElementId> filteredGroup;
            for (const ElementId& elementId : group)
            {
                if (connectedElements.find(elementId) != connectedElements.end())
                {
                    filteredGroup.push_back(elementId);
                }
            }
            // 只要过滤后的组不为空就保留
            if (!filteredGroup.empty())
            {
                filteredGroups.push_back(filteredGroup);
            }
        }
        connectedElementGroups = filteredGroups;
    }

    // 按Element分组计算器
    std::map<ElementId, std::vector<size_t>> elementGroups;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        elementGroups[calculatorInfos[i].pElement->GetElementId()].push_back(i);
    }

    // 收集相关计算器的RegenDataId
    std::set<std::wstring> allRegenDataIds;
    std::set<size_t> relevantCalculators;

    // 首先收集所有相关的计算器索引
    if (m_onlyMultipleElements)
    {
        // 只收集在组中的计算器
        for (const auto& connectedElementGroup : connectedElementGroups)
        {
            for (const ElementId& elementId : connectedElementGroup)
            {
                for (size_t i = 0; i < calculatorInfos.size(); i++)
                {
                    if (calculatorInfos[i].pElement->GetElementId() == elementId)
                    {
                        // 如果指定了目标ElementId，只收集在路径上的计算器
                        if (m_targetElementId.IsValid())
                        {
                            if (calculatorsOnPath.find(i) != calculatorsOnPath.end())
                            {
                                relevantCalculators.insert(i);
                            }
                        }
                        else
                        {
                            relevantCalculators.insert(i);
                        }
                    }
                }
            }
        }
    }
    else
    {
        // 收集所有计算器，但如果指定了目标ElementId，则只收集在路径上的计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            ElementId calcElementId = calculatorInfos[i].pElement->GetElementId();

            // 如果指定了目标ElementId，只收集在路径上的计算器
            if (m_targetElementId.IsValid())
            {
                if (calculatorsOnPath.find(i) != calculatorsOnPath.end())
                {
                    relevantCalculators.insert(i);
                }
            }
            else
            {
                relevantCalculators.insert(i);
            }
        }
    }

    // 收集相关计算器的RegenDataId
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 收集输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        allRegenDataIds.insert(outputKey);

        // 收集输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            allRegenDataIds.insert(inputKey);
        }
    }

    // 创建图元分组结构：直接显示图元 -> 计算器
    size_t elementIndex = 0;
    std::set<ElementId> processedElements;

    // 处理连通的图元组
    for (const auto& connectedElementGroup : connectedElementGroups)
    {
        for (const ElementId& elementId : connectedElementGroup)
        {
            processedElements.insert(elementId);

            // 找到属于这个图元的所有相关计算器
            std::vector<size_t> calculatorIndices;
            for (size_t i = 0; i < calculatorInfos.size(); i++)
            {
                if (calculatorInfos[i].pElement->GetElementId() == elementId &&
                    relevantCalculators.find(i) != relevantCalculators.end())
                {
                    calculatorIndices.push_back(i);
                }
            }

            if (!calculatorIndices.empty())
            {
                std::wstring elementIdStr = std::to_wstring(elementId.AsInt64());
                std::wstring elementType = GetElementTypeName(calculatorInfos[calculatorIndices[0]].pElement);

                // 创建图元子图
                ss << L"    subgraph Element" << elementIndex
                   << L"[\"" << elementType << L":" << elementIdStr << L"\"]\n";

                // 在图元子图中创建计算器节点
                for (size_t calcIndex : calculatorIndices)
                {
                    const CalculatorInfo& info = calculatorInfos[calcIndex];
                    std::wstring nodeId = L"calc" + std::to_wstring(calcIndex);

                    ss << L"        " << nodeId << L"[\"" << info.calculatorName << L"\"]\n";
                }

                ss << L"    end\n";
                elementIndex++;
            }
        }
    }

    // 处理孤立的图元（没有跨图元连接的图元）
    if (!m_onlyMultipleElements)
    {
        for (const auto& elementGroup : elementGroups)
        {
            ElementId elementId = elementGroup.first;
            const std::vector<size_t>& calculatorIndices = elementGroup.second;

            // 检查这个图元是否已经被处理（在某个组中）
            if (processedElements.find(elementId) == processedElements.end())
            {
                // 如果指定了目标ElementId，只处理有连接关系的图元
                if (m_targetElementId.IsValid() && connectedElements.find(elementId) == connectedElements.end())
                {
                    continue;
                }

                // 找到属于这个图元的所有相关计算器
                std::vector<size_t> relevantCalculatorIndices;
                for (size_t calcIndex : calculatorIndices)
                {
                    if (relevantCalculators.find(calcIndex) != relevantCalculators.end())
                    {
                        relevantCalculatorIndices.push_back(calcIndex);
                    }
                }

                if (!relevantCalculatorIndices.empty())
                {
                    std::wstring elementIdStr = std::to_wstring(elementId.AsInt64());
                    std::wstring elementType = GetElementTypeName(calculatorInfos[relevantCalculatorIndices[0]].pElement);

                    // 创建独立的图元子图
                    ss << L"    subgraph Element" << elementIndex << L"[\"" << elementType << L":" << elementIdStr << L"\"]\n";

                    for (size_t calcIndex : relevantCalculatorIndices)
                    {
                        const CalculatorInfo& info = calculatorInfos[calcIndex];
                        std::wstring nodeId = L"calc" + std::to_wstring(calcIndex);

                        ss << L"        " << nodeId << L"[\"" << info.calculatorName << L"\"]\n";
                    }

                    ss << L"    end\n";
                    elementIndex++;
                }
            }
        }
    }

    // 创建RegenDataId节点ID映射和实际RegenDataId对象的映射
    std::map<std::wstring, std::wstring> regenDataIdToNodeId;
    std::map<std::wstring, RegenDataId> keyToRegenDataId;

    // 建立key到RegenDataId对象的映射
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        keyToRegenDataId[outputKey] = info.outputDataId;

        // 输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            keyToRegenDataId[inputKey] = inputDataId;
        }
    }

    // 过滤掉没有连接到任何相关计算器的RegenDataId
    std::set<std::wstring> connectedRegenDataIds;
    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 收集输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        connectedRegenDataIds.insert(outputKey);

        // 收集输入RegenDataId
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            connectedRegenDataIds.insert(inputKey);
        }
    }

    // 进一步过滤：移除孤立的RegenDataId（没有连接到任何相关计算器的RegenDataId）
    std::set<std::wstring> finalRegenDataIds;
    for (const std::wstring& regenDataKey : connectedRegenDataIds)
    {
        bool isConnected = false;

        // 检查是否有相关计算器输出这个RegenDataId
        for (size_t i : relevantCalculators)
        {
            const CalculatorInfo& info = calculatorInfos[i];
            std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
            if (outputKey == regenDataKey)
            {
                isConnected = true;
                break;
            }
        }

        // 检查是否有相关计算器使用这个RegenDataId作为输入
        if (!isConnected)
        {
            for (size_t i : relevantCalculators)
            {
                const CalculatorInfo& info = calculatorInfos[i];
                for (const RegenDataId& inputDataId : info.inputDataIds)
                {
                    std::wstring inputKey = RegenDataIdToKey(inputDataId);
                    if (inputKey == regenDataKey)
                    {
                        isConnected = true;
                        break;
                    }
                }
                if (isConnected) break;
            }
        }

        if (isConnected)
        {
            finalRegenDataIds.insert(regenDataKey);
        }
    }

    connectedRegenDataIds = finalRegenDataIds;

    size_t dataNodeIndex = 0;
    for (const std::wstring& regenDataKey : connectedRegenDataIds)
    {
        std::wstring nodeId = L"data_" + std::to_wstring(dataNodeIndex);
        regenDataIdToNodeId[regenDataKey] = nodeId;
        dataNodeIndex++;

        // 使用可读信息显示RegenDataId节点
        auto it = keyToRegenDataId.find(regenDataKey);
        std::wstring displayInfo;
        if (it != keyToRegenDataId.end())
        {
            displayInfo = GetRegenDataIdReadableInfo(it->second);
        }
        else
        {
            // 如果找不到对应的RegenDataId对象，使用原始格式
            displayInfo = regenDataKey;
            size_t pos = 0;
            while ((pos = displayInfo.find(L"_", pos)) != std::wstring::npos) {
                if (pos == displayInfo.find(L"_")) {
                    displayInfo.replace(pos, 1, L",Data:");
                    pos += 6;
                } else {
                    displayInfo.replace(pos, 1, L",Ext:");
                    pos += 5;
                }
            }
            displayInfo = L"Obj:" + displayInfo;
        }

        ss << L"    " << nodeId << L"[\"" << displayInfo << L"\"]\n";
    }

    // 创建连接：计算器 -> 输出RegenDataId -> 输入计算器
    std::set<std::wstring> connections;

    for (size_t i : relevantCalculators)
    {
        const CalculatorInfo& info = calculatorInfos[i];
        std::wstring calcNodeId = L"calc" + std::to_wstring(i);

        // 计算器 -> 输出RegenDataId
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        auto outputNodeIt = regenDataIdToNodeId.find(outputKey);
        if (outputNodeIt != regenDataIdToNodeId.end())
        {
            std::wstring connection = calcNodeId + L" --> " + outputNodeIt->second;
            if (connections.find(connection) == connections.end())
            {
                ss << L"    " << connection << L"\n";
                connections.insert(connection);
            }
        }

        // 输入RegenDataId -> 计算器
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            auto inputNodeIt = regenDataIdToNodeId.find(inputKey);
            if (inputNodeIt != regenDataIdToNodeId.end())
            {
                std::wstring connection = inputNodeIt->second + L" --> " + calcNodeId;
                if (connections.find(connection) == connections.end())
                {
                    ss << L"    " << connection << L"\n";
                    connections.insert(connection);
                }
            }
        }
    }

    // 添加RegenDataId节点的样式定义
    if (!regenDataIdToNodeId.empty())
    {
        ss << L"\n    %% RegenDataId节点样式\n";
        ss << L"    classDef regenDataStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000\n";
        ss << L"    classDef regenDataGrayStyle fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px,color:#666\n";

        // 如果指定了目标ElementId，需要区分来自目标ElementId的RegenDataId和其他的
        if (m_targetElementId.IsValid())
        {
            // 收集来自目标ElementId计算器的输出RegenDataId
            std::set<std::wstring> targetElementOutputs;
            for (size_t i = 0; i < calculatorInfos.size(); i++)
            {
                if (calculatorInfos[i].pElement->GetElementId() == m_targetElementId)
                {
                    std::wstring outputKey = RegenDataIdToKey(calculatorInfos[i].outputDataId);
                    targetElementOutputs.insert(outputKey);
                }
            }

            for (const auto& pair : regenDataIdToNodeId)
            {
                const std::wstring& regenDataKey = pair.first;
                const std::wstring& nodeId = pair.second;

                // 如果这个RegenDataId来自目标ElementId，使用蓝色样式，否则使用灰色样式
                if (targetElementOutputs.find(regenDataKey) != targetElementOutputs.end())
                {
                    ss << L"    class " << nodeId << L" regenDataStyle\n";
                }
                else
                {
                    ss << L"    class " << nodeId << L" regenDataGrayStyle\n";
                }
            }
        }
        else
        {
            // 没有指定目标ElementId时，所有RegenDataId使用蓝色样式
            for (const auto& pair : regenDataIdToNodeId)
            {
                const std::wstring& nodeId = pair.second;
                ss << L"    class " << nodeId << L" regenDataStyle\n";
            }
        }
    }

    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::RegenDataIdToString(const RegenDataId& dataId)
{
    std::wstringstream ss;
    ss << L"Obj:" << dataId.ObjectId << L",Data:" << dataId.DataId;
    if (dataId.ExtendedId != -1)
    {
        ss << L",Ext:" << dataId.ExtendedId;
    }
    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::RegenDataIdToKey(const RegenDataId& dataId)
{
    std::wstringstream ss;
    ss << dataId.ObjectId << L"_" << dataId.DataId << L"_" << dataId.ExtendedId;
    return ss.str();
}

std::wstring CmdCalculatorGraphAnalyzer::GetRegenDataIdReadableInfo(const RegenDataId& dataId)
{
    std::wstringstream ss;

    IDocument* pDocument = UiDocumentViewUtils::GetCurrentDocument();
    IElement* pElement = pDocument->GetElement(ElementId(dataId.ObjectId));
    if (pElement)
    {
        ss << GetElementTypeName(pElement) << L"#" << dataId.ObjectId;
    }
    else
    {
        ss << L"nullptr:" << dataId.ObjectId;
    }

    // 获取可读的DataId信息
    const gcmp::RegenDataIdInfo& dataIdInfo = gcmp::RegenDataIdManager::Get().GetRegenDataIdInfo((int)dataId.DataId);
    if (dataIdInfo.Id != -1 && !dataIdInfo.Name.empty())
    {
        // 如果有可读信息，显示名称和类名
        ss << L",<br/>Data:";
        if (!dataIdInfo.ClassName.empty())
        {
            ss << dataIdInfo.ClassName << L"#";
        }
        ss << dataIdInfo.Name;
    }
    else
    {
        // 如果没有可读信息，显示原始DataId
        ss << L",<br/>Data:" << dataId.DataId;
    }

    if (dataId.ExtendedId != -1)
    {
        ss << L",<br/>Ext:" << dataId.ExtendedId;
    }
    std::wstring rslt = ss.str();
    StringUtil::ReplaceAll(rslt, L"\"\"", L"::");
    StringUtil::ReplaceAll(rslt, L"\"", L"");
    StringUtil::ReplaceAll(rslt, L" + ", L"::");
    return rslt;
}

void CmdCalculatorGraphAnalyzer::DFSFindElementGroup(const ElementId& elementId, const std::map<ElementId, std::vector<ElementId>>& connections,
                                                   std::set<ElementId>& visited, std::vector<ElementId>& group)
{
    visited.insert(elementId);
    group.push_back(elementId);

    // 遍历所有连接的图元
    auto it = connections.find(elementId);
    if (it != connections.end())
    {
        for (const ElementId& connectedElementId : it->second)
        {
            if (visited.find(connectedElementId) == visited.end())
            {
                DFSFindElementGroup(connectedElementId, connections, visited, group);
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindReachableElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                      std::set<ElementId>& reachableElements)
{
    // 首先添加目标ElementId本身
    reachableElements.insert(targetElementId);

    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators; // RegenDataId -> 输出该数据的计算器列表
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;  // 计算器索引 -> 输入的RegenDataId列表
    std::map<size_t, std::wstring> calculatorToOutput;              // 计算器索引 -> 输出的RegenDataId

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用BFS向前追溯（找输入链）和向后追溯（找输出链）
    std::queue<ElementId> toProcess;
    toProcess.push(targetElementId);

    while (!toProcess.empty())
    {
        ElementId currentElementId = toProcess.front();
        toProcess.pop();

        // 找到属于当前图元的所有计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (calculatorInfos[i].pElement->GetElementId() == currentElementId)
            {
                // 向前追溯：找到为当前计算器提供输入的图元
                for (const std::wstring& inputKey : calculatorToInputs[i])
                {
                    auto it = outputToCalculators.find(inputKey);
                    if (it != outputToCalculators.end())
                    {
                        for (size_t sourceCalcIndex : it->second)
                        {
                            ElementId sourceElementId = calculatorInfos[sourceCalcIndex].pElement->GetElementId();
                            if (reachableElements.find(sourceElementId) == reachableElements.end())
                            {
                                reachableElements.insert(sourceElementId);
                                toProcess.push(sourceElementId);
                            }
                        }
                    }
                }

                // 向后追溯：找到使用当前计算器输出的图元
                std::wstring outputKey = calculatorToOutput[i];
                for (size_t j = 0; j < calculatorInfos.size(); j++)
                {
                    for (const std::wstring& inputKey : calculatorToInputs[j])
                    {
                        if (inputKey == outputKey)
                        {
                            ElementId targetCalcElementId = calculatorInfos[j].pElement->GetElementId();
                            if (reachableElements.find(targetCalcElementId) == reachableElements.end())
                            {
                                reachableElements.insert(targetCalcElementId);
                                toProcess.push(targetCalcElementId);
                            }
                        }
                    }
                }
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindConnectedElements(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                      std::set<ElementId>& connectedElements)
{
    // 添加目标ElementId本身
    connectedElements.insert(targetElementId);

    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators;
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;
    std::map<size_t, std::wstring> calculatorToOutput;

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用BFS找到所有有连接关系的图元
    std::queue<ElementId> toProcess;
    toProcess.push(targetElementId);

    while (!toProcess.empty())
    {
        ElementId currentElementId = toProcess.front();
        toProcess.pop();

        // 找到属于当前图元的所有计算器
        for (size_t i = 0; i < calculatorInfos.size(); i++)
        {
            if (calculatorInfos[i].pElement->GetElementId() == currentElementId)
            {
                // 向前追溯：找到为当前计算器提供输入的图元
                for (const std::wstring& inputKey : calculatorToInputs[i])
                {
                    auto it = outputToCalculators.find(inputKey);
                    if (it != outputToCalculators.end())
                    {
                        for (size_t sourceCalcIndex : it->second)
                        {
                            ElementId sourceElementId = calculatorInfos[sourceCalcIndex].pElement->GetElementId();
                            if (connectedElements.find(sourceElementId) == connectedElements.end())
                            {
                                connectedElements.insert(sourceElementId);
                                toProcess.push(sourceElementId);
                            }
                        }
                    }
                }

                // 向后追溯：找到使用当前计算器输出的图元
                std::wstring outputKey = calculatorToOutput[i];
                for (size_t j = 0; j < calculatorInfos.size(); j++)
                {
                    for (const std::wstring& inputKey : calculatorToInputs[j])
                    {
                        if (inputKey == outputKey)
                        {
                            ElementId targetCalcElementId = calculatorInfos[j].pElement->GetElementId();
                            if (connectedElements.find(targetCalcElementId) == connectedElements.end())
                            {
                                connectedElements.insert(targetCalcElementId);
                                toProcess.push(targetCalcElementId);
                            }
                        }
                    }
                }
            }
        }
    }
}

void CmdCalculatorGraphAnalyzer::FindCalculatorsOnPathToTarget(const std::vector<CalculatorInfo>& calculatorInfos, const ElementId& targetElementId,
                                                             std::set<size_t>& calculatorsOnPath)
{
    // 建立计算器的输入输出映射
    std::map<std::wstring, std::vector<size_t>> outputToCalculators; // RegenDataId -> 输出该数据的计算器列表
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;  // 计算器索引 -> 输入的RegenDataId列表
    std::map<size_t, std::wstring> calculatorToOutput;              // 计算器索引 -> 输出的RegenDataId

    // 建立映射关系
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);
        calculatorToOutput[i] = outputKey;

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 使用两阶段算法：
    // 1. 从目标图元向上游追溯，找到所有能影响目标图元的计算器
    // 2. 从目标图元向下游追溯，找到所有受目标图元影响的计算器

    std::set<size_t> upstreamCalculators;   // 上游计算器（影响目标图元的）
    std::set<size_t> downstreamCalculators; // 下游计算器（受目标图元影响的）

    // 首先添加目标图元的所有计算器
    std::set<size_t> targetCalculators;
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (calculatorInfos[i].pElement->GetElementId() == targetElementId)
        {
            targetCalculators.insert(i);
            calculatorsOnPath.insert(i);
        }
    }

    // 第一阶段：向上游追溯（找到能影响目标图元的计算器）
    std::queue<size_t> upstreamQueue;
    for (size_t targetCalc : targetCalculators)
    {
        upstreamQueue.push(targetCalc);
    }

    while (!upstreamQueue.empty())
    {
        size_t currentCalcIndex = upstreamQueue.front();
        upstreamQueue.pop();

        // 向前追溯：找到为当前计算器提供输入的计算器
        for (const std::wstring& inputKey : calculatorToInputs[currentCalcIndex])
        {
            auto it = outputToCalculators.find(inputKey);
            if (it != outputToCalculators.end())
            {
                for (size_t sourceCalcIndex : it->second)
                {
                    if (upstreamCalculators.find(sourceCalcIndex) == upstreamCalculators.end())
                    {
                        upstreamCalculators.insert(sourceCalcIndex);
                        upstreamQueue.push(sourceCalcIndex);
                    }
                }
            }
        }
    }

    // 第二阶段：向下游追溯（找到受目标图元影响的计算器）
    std::queue<size_t> downstreamQueue;
    for (size_t targetCalc : targetCalculators)
    {
        downstreamQueue.push(targetCalc);
    }

    while (!downstreamQueue.empty())
    {
        size_t currentCalcIndex = downstreamQueue.front();
        downstreamQueue.pop();

        // 向后追溯：找到使用当前计算器输出的计算器
        std::wstring outputKey = calculatorToOutput[currentCalcIndex];
        for (size_t j = 0; j < calculatorInfos.size(); j++)
        {
            for (const std::wstring& inputKey : calculatorToInputs[j])
            {
                if (inputKey == outputKey)
                {
                    if (downstreamCalculators.find(j) == downstreamCalculators.end())
                    {
                        downstreamCalculators.insert(j);
                        downstreamQueue.push(j);
                    }
                }
            }
        }
    }

    // 第三阶段：验证路径有效性，只保留真正有直接路径连接的计算器
    std::set<size_t> validCalculators;

    // 添加目标计算器
    for (size_t calc : targetCalculators)
    {
        validCalculators.insert(calc);
    }

    // 验证上游计算器：必须有直接路径到达目标计算器
    for (size_t upstreamCalc : upstreamCalculators)
    {
        for (size_t targetCalc : targetCalculators)
        {
            std::set<size_t> visited;
            if (HasDirectPathBetweenCalculators(calculatorInfos, upstreamCalc, targetCalc, visited))
            {
                validCalculators.insert(upstreamCalc);
                break;
            }
        }
    }

    // 验证下游计算器：目标计算器必须有直接路径到达它们
    for (size_t downstreamCalc : downstreamCalculators)
    {
        for (size_t targetCalc : targetCalculators)
        {
            std::set<size_t> visited;
            if (HasDirectPathBetweenCalculators(calculatorInfos, targetCalc, downstreamCalc, visited))
            {
                validCalculators.insert(downstreamCalc);
                break;
            }
        }
    }

    // 最终结果
    calculatorsOnPath = validCalculators;
}

bool CmdCalculatorGraphAnalyzer::HasDirectPathBetweenCalculators(const std::vector<CalculatorInfo>& calculatorInfos,
                                                            size_t fromCalc, size_t toCalc, std::set<size_t>& visited)
{
    // 如果已经访问过这个计算器，避免无限循环
    if (visited.find(fromCalc) != visited.end())
    {
        return false;
    }

    // 如果到达目标计算器
    if (fromCalc == toCalc)
    {
        return true;
    }

    // 标记当前计算器为已访问
    visited.insert(fromCalc);

    // 建立输出到计算器的映射（仅用于当前路径查找）
    std::map<std::wstring, std::vector<size_t>> outputToCalculators;
    std::map<size_t, std::vector<std::wstring>> calculatorToInputs;

    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        const CalculatorInfo& info = calculatorInfos[i];

        // 输出映射
        std::wstring outputKey = RegenDataIdToKey(info.outputDataId);
        outputToCalculators[outputKey].push_back(i);

        // 输入映射
        for (const RegenDataId& inputDataId : info.inputDataIds)
        {
            std::wstring inputKey = RegenDataIdToKey(inputDataId);
            calculatorToInputs[i].push_back(inputKey);
        }
    }

    // 获取当前计算器的输出
    const CalculatorInfo& fromInfo = calculatorInfos[fromCalc];
    std::wstring fromOutputKey = RegenDataIdToKey(fromInfo.outputDataId);

    // 查找使用当前计算器输出的所有计算器
    for (size_t i = 0; i < calculatorInfos.size(); i++)
    {
        if (i == fromCalc) continue; // 跳过自己

        for (const std::wstring& inputKey : calculatorToInputs[i])
        {
            if (inputKey == fromOutputKey)
            {
                // 找到直接连接的计算器，递归检查
                if (HasDirectPathBetweenCalculators(calculatorInfos, i, toCalc, visited))
                {
                    return true;
                }
            }
        }
    }

    // 移除访问标记，允许其他路径访问这个计算器
    visited.erase(fromCalc);
    return false;
}

std::wstring CmdCalculatorGraphAnalyzer::GetElementTypeName(const IElement* pElement)
{
    std::wstring rslt = StringUtil::ToWString(typeid(*pElement).name());
    StringUtil::ReplaceAll(rslt, L"class ", L"");
    return rslt;
}

REGISTER_COMMAND(CmdCalculatorGraphAnalyzer);
