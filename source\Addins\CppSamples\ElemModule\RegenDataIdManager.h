﻿// Owner: yangt-i
// Co-Owner:

#pragma once
#include "GcmpModel.h"

#pragma warning (push)
#pragma warning (disable:4251)

namespace gcmp
{
    class GCMP_MODEL_EXPORT RegenDataIdInfo
    {
    public:
        // 唯一Id
        int Id = -1;
        // Id的名字
        std::wstring Name;
        // 所在类名
        std::wstring ClassName;

        static const RegenDataIdInfo InvalidRegenDataIdInfo;
    };

    class GCMP_MODEL_EXPORT RegenDataIdManager
    {
    public:
        static RegenDataIdManager& Get() {static RegenDataIdManager manager; return manager;}
        int RegisterRegenDataIdInfo(const std::wstring& name, const std::wstring& className);
        const RegenDataIdInfo& GetRegenDataIdInfo(int id) const;
    private:
        RegenDataIdManager() {};
    private:
        std::vector<RegenDataIdInfo> m_idInfos;
    };

}

#pragma warning (pop)


