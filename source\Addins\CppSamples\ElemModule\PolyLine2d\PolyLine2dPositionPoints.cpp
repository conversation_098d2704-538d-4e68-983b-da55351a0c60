﻿#include "PolyLine2dPositionPoints.h"
#include "ILine2d.h"
#include "ICurve2d.h"
#include "IDocument.h"
#include "IPolyCurve.h"
#include "IRegenerator.h"
#include "DbObjectUtils.h"
#include "IGenericElement.h"
#include "IElementModelShape.h"
#include "UiDocumentViewUtils.h"
#include "IElementBasicInformation.h"
#include "IRegeneratorDataIdCreator.h"

#include "PolyLine2d.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

DBOBJECT_DATA_DEFINE(PolyLine2dPositionPoints)
{
    SetOwnerElement(nullptr);
}

PolyLine2d* PolyLine2dPositionPoints::GetPolyLine2d()
{
    IGenericElement *pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pGenericElement, L"GenericElement不能为空",L"GDMPLab",L"2024-12-30");
    PolyLine2d* pPolyLine2d = quick_cast<PolyLine2d>(pGenericElement->GetExternalObject());
    return pPolyLine2d;
}

const PolyLine2d* PolyLine2dPositionPoints::GetPolyLine2d() const
{
    const IGenericElement *pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pGenericElement, L"GenericElement不能为空",L"GDMPLab",L"2024-12-30");
    const PolyLine2d* pPolyLine2d = quick_cast<PolyLine2d>(pGenericElement->GetExternalObject());
    return pPolyLine2d;
}

bool PolyLine2dPositionPoints::SetOwnerElement(IElement* pOwnerElement)
{
    m_pOwnerElement = pOwnerElement;
    return true;
}

IElement* PolyLine2dPositionPoints::GetOwnerElement()
{
    return m_pOwnerElement.Get();
}

const IElement* PolyLine2dPositionPoints::GetOwnerElement() const
{
    return m_pOwnerElement.Get();
}

int PolyLine2dPositionPoints::GetControlPointCount() const
{
    const PolyLine2d* pPolyLine2d = GetPolyLine2d();
    DBG_WARN_AND_RETURN_UNLESS(pPolyLine2d,0, L"PolyLine2d获取失败",L"GDMPLab",L"2024-12-30");
    const gcmp::IPolyCurve* pBoundaryCurve = pPolyLine2d->GetBoundaryCurve();
    DBG_WARN_AND_RETURN_UNLESS(pBoundaryCurve, 0, L"pBoundaryCurve获取失败",L"GDMPLab",L"2024-12-30");
    int controlPointCount = pBoundaryCurve->GetCurveCount()+1;
    return controlPointCount;
}

gcmp::Vector3d PolyLine2dPositionPoints::GetControlPoint(int index) const
{
    DBG_WARN_AND_RETURN_UNLESS(index < GetControlPointCount(), Vector3d::Zero,L"索引不能超过控制点个数",L"GDMPLab",L"2024-12-30");
    const PolyLine2d* pPolyLine2d = GetPolyLine2d();
    DBG_WARN_AND_RETURN_UNLESS(pPolyLine2d, Vector3d::Zero, L"PolyLine2d获取失败",L"GDMPLab",L"2024-12-30");
    const gcmp::IPolyCurve* pBoundaryCurve = pPolyLine2d->GetBoundaryCurve();
    DBG_WARN_AND_RETURN_UNLESS(pBoundaryCurve, Vector3d::Zero, L"pBoundaryCurve获取失败",L"GDMPLab",L"2024-12-30");
    Vector2d point;
    if (index != 0)
       point = pBoundaryCurve->GetCurve(index-1)->GetEndPoint();
    else
       point = pBoundaryCurve->GetCurve(0)->GetStartPoint();
    Coordinate3d coordinate = pPolyLine2d->GetCoordinate();
    Vector3d result = coordinate.GetWorldPoint(Vector3d(point, 0));
    return result;
}

void PolyLine2dPositionPoints::SetControlPoint(int index, const Vector3d& point)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(index < GetControlPointCount(), L"索引不能超过控制点个数",L"GDMPLab",L"2024-12-30");
    PolyLine2d* pPolyLine2d = GetPolyLine2d();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pPolyLine2d,L"PolyLine2d获取失败",L"GDMPLab",L"2024-12-30");
    gcmp::IPolyCurve* pBoundaryCurve = pPolyLine2d->GetBoundaryCurve();
    Coordinate3d coordinate = pPolyLine2d->GetCoordinate();
    Vector2d point2d = coordinate.GetLocalPoint(point).Vec2d();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pBoundaryCurve, L"pBoundaryCurve获取失败",L"GDMPLab",L"2024-12-30");
    if (index == 0)
    {
        OwnerPtr<ICurve2d> opCurve = pBoundaryCurve->GetCurve(0);
        OwnerPtr<ICurve2d> opLine = ILine2d::Create(point2d,opCurve->GetEndPoint());
        pBoundaryCurve->ReplaceCurve(0, TransferOwnership(opLine));
     }
     else
    {
        OwnerPtr<ICurve2d> opCurve = pBoundaryCurve->GetCurve(index-1);
        OwnerPtr<ICurve2d> opLine = ILine2d::Create(opCurve->GetStartPoint(), point2d);
        pBoundaryCurve->ReplaceCurve(index-1, TransferOwnership(opLine));
       if (index != pBoundaryCurve->GetCurveCount())
        {
            opCurve = pBoundaryCurve->GetCurve(index);
            opLine = ILine2d::Create(point2d, opCurve->GetEndPoint());
            pBoundaryCurve->ReplaceCurve(index, TransferOwnership(opLine));
        }
     }
     pPolyLine2d->SetBoundaryCurve(TransferOwnershipCast<IPolyCurve>(pBoundaryCurve->Clone()));
     pPolyLine2d->MarkBoundaryCurveRdId();

    IGenericElement *pGenericElement = quick_cast<IGenericElement>(GetOwnerElement());
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGenericElement, L"GenericElement获取失败",L"GDMPLab",L"2024-12-30");
    if (const IElementPositionPoints* pElementPositionPoints = pGenericElement->GetPositionPoints())
    {
        for (int i = 0; i < pElementPositionPoints->GetControlPointCount(); ++i)
        {
            MarkControlPointRdId(i);
        }
     }
}

RegenDataId PolyLine2dPositionPoints::GetControlPointRdId(int index) const
{
    static int dataId = IRegeneratorDataIdCreator::CreateRegenDataId(
    WSTRING_OF("PolyLine2dPositionPoints"),
    WSTRING_OF("Sample_PolyLine2dPositionPoints"));
    const IElement* pOwnerElement = GetOwnerElement();
    DBG_WARN_AND_RETURN_UNLESS(pOwnerElement, RegenDataId(), L"Element获取失败",L"GDMPLab",L"2024-12-30");
    const IElementBasicInformation* pElementBasicInformation = pOwnerElement->GetBasicInformation();
    DBG_WARN_AND_RETURN_UNLESS(pElementBasicInformation, RegenDataId(), L"ElementBasicInformation获取失败",L"GDMPLab",L"2024-12-30");
    return RegenDataId(pElementBasicInformation->GetElementId().AsInt64(), dataId, index);
}

void PolyLine2dPositionPoints::MarkControlPointRdId(int index)
{
    const IElement* pOwnerElement = GetOwnerElement();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pOwnerElement,L"Element获取失败",L"GDMPLab",L"2024-12-30");
    IDocument* pDocument = pOwnerElement->GetDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDocument, L"Document获取失败",L"GDMPLab",L"2024-12-30");
    IRegenerator* pRegenerator = pDocument->GetRegenerator();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pRegenerator, L"Regenerator获取失败",L"GDMPLab",L"2024-12-30");
    pRegenerator->MarkRegenDataId(GetControlPointRdId(index));
}

