# CmdCalculatorGraphAnalyzer - 计算器关联图分析器

## 概述

CmdCalculatorGraphAnalyzer是一个GDMP Command，用于分析文档中所有图元的关联更新机制，并生成Mermaid格式的可视化图表。

## 核心功能

### 1. 图元遍历
- 通过`IDocument::GetAllElements()`获取文档中的所有图元
- 对每个图元调用`GetElementRegenerationComponent()`获取关联更新组件

### 2. 计算器收集
- 使用`ICalculatorCollection::Create()`创建计算器收集器
- 调用`GetCalculators()`收集图元的所有计算器
- 通过`GetCalculatorCount()`和`GetCalcualtorByIndex()`遍历计算器

### 3. 依赖关系分析
- 通过`GetRegenDataId()`获取计算器的输出数据ID
- 通过`ReportInputDataIds()`获取计算器的输入数据ID列表
- 使用`RegenDataId`的`ObjectId`属性关联图元

### 4. Mermaid图生成
- 为每个计算器创建节点，显示计算器名称、图元类型和ID
- 根据输入输出关系创建有向连接
- 生成标准Mermaid语法的图形描述

## 关键数据结构

```cpp
struct CalculatorInfo
{
    std::wstring calculatorName;    // 计算器名称
    std::wstring elementId;         // 所属图元ID
    std::wstring elementType;       // 图元类型
    RegenDataId outputDataId;       // 输出数据ID
    std::vector<RegenDataId> inputDataIds; // 输入数据ID列表
};
```

## 核心算法

### 依赖关系建立
1. 为每个计算器的输出数据ID创建映射表
2. 遍历每个计算器的输入数据ID
3. 在映射表中查找对应的输出计算器
4. 建立"输出计算器 → 当前计算器"的连接

### RegenDataId比较
- 使用完整的RegenDataId进行比较（ObjectId + DataId + ExtendedId）
- 通过ObjectId可以确定数据所属的图元
- 通过DataId可以确定具体的数据类型

## 输出格式

生成的Mermaid图包含：
- 节点：显示计算器名称和所属图元信息
- 连接：显示计算器之间的依赖关系
- 样式：可以为不同类型的图元使用不同颜色

示例输出：
```mermaid
graph TD
    calc0["墙参数计算器<br/>(Wall:12345)"]
    calc1["墙图形计算器<br/>(Wall:12345)"]
    calc0 --> calc1
```

## 使用方法

1. 在GDMP应用中注册Command：
   ```cpp
   REGISTER_COMMAND(CmdCalculatorGraphAnalyzer);
   ```

2. 执行Command后会在当前目录生成`calculator_graph.md`文件

3. 使用支持Mermaid的工具查看图形：
   - Typora
   - VS Code (with Mermaid extension)
   - GitHub/GitLab
   - 在线Mermaid编辑器

## 技术要点

### GDMP接口使用
- `IDocument::GetAllElements()` - 获取所有图元
- `IElement::GetElementRegenerationComponent()` - 获取关联更新组件
- `IElementRegenerationComponent::GetCalculators()` - 获取计算器
- `ICalculator::GetRegenDataId()` - 获取输出数据ID
- `ICalculator::ReportInputDataIds()` - 获取输入数据ID

### 数据处理
- 使用STL容器（vector、map、set）管理数据
- 避免重复连接的处理
- Unicode字符串处理（std::wstring）

### 错误处理
- 检查空指针和无效状态
- 处理文件保存失败的情况
- 提供用户友好的错误提示

## 扩展可能

1. **过滤功能**：只分析特定类型的图元或计算器
2. **图形美化**：为不同类型使用不同的颜色和形状
3. **交互式输出**：生成HTML格式的交互式图形
4. **性能优化**：对大型文档进行分批处理
5. **详细信息**：在节点中显示更多计算器和数据信息

## 学习价值

这个Command展示了：
- GDMP关联更新机制的核心概念
- 如何遍历和分析图元的计算器
- RegenDataId在依赖关系中的作用
- 如何将复杂的数据关系可视化
- GDMP Command的标准实现模式
